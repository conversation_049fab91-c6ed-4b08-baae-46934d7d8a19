以下是论文《大规模群体拦截场景中多无人机系统的分层任务分配》的中文翻译，包含所有公式和算法：

---

### 无人机

#### 文章  
**大规模群体拦截场景中多无人机系统的分层任务分配**

吴新宁，张梦鸽，王祥科，郑永斌，余黄超  
国防科技大学智能科学与技术学院，长沙 410073  
*通讯作者：<EMAIL>  

**摘要**：大规模群体拦截场景中的多无人机任务分配问题面临计算复杂度高和缺乏精确评估模型的挑战。本文提出了一种有效的评估模型和分层任务分配框架来解决这些问题。该评估模型结合了固定翼无人机的动态约束，并改进了阿波罗尼斯圆模型，以准确描述多无人机的协同拦截效果。通过评估拦截过程中的拦截效果，基于该模型可以给出多无人机的分配方案。为了优化无人机与目标的配置，采用了基于网络流算法的分层框架。该框架利用基于特征相似性和拦截优势的聚类方法，将大规模任务分配问题分解为多个完整的子模型。分配完成后，通过Dubins曲线规划最优拦截点，确保拦截任务的有效性。仿真结果验证了所提方案的可行性和有效性。随着模型规模的增大，所提方案的运行时间下降率更高。在200架无人机和100个目标的大规模场景中，运行时间减少了84.86%。

**关键词**：任务分配；多无人机系统；大规模分配  

---

### 1. 引言  
反无人机技术已成为热门话题，随着无人机攻击的增多及其在冲突中的应用[1]。传统的反无人机方法包括火力打击和电磁干扰等。随着低成本无人机规模的扩大，传统反无人机方法已失效。面对大规模无人机攻击，可通过构建敌对无人机群实现主动拦截[2]。这种新型反无人机技术涵盖智能决策与控制等先进技术，具有一定优势。对于大规模无人机攻击，基于固定翼无人机平台的拦截将成为最重要的反制手段之一[3,4]。任务分配作为多无人机系统的核心组成部分，在拦截场景中对目标和无人机的优化配置至关重要[5]。一旦检测到目标，无人机需根据双方态势尽快提供拦截方案。在大规模拦截场景中，分配算法需满足可靠评估模型和高效求解速度的前提条件。解决大规模群体拦截场景中的任务分配问题仍是一个挑战。

在大规模群体拦截场景中，多无人机通过评估相关信息形成分配计划。该问题具有以下特点：  
- 评估模型需有效评估攻防态势，并将任务分配结果与拦截技术结合，确保任务成功执行。  
- 大规模拦截场景中存在聚类目标，无人机需协同完成任务，且执行能力不同。  
- 任务分配算法需在短时间内提供可行解，以适应固定翼无人机的高速运动。

评估模型的建立是任务分配的基础。合理的评估模型是确保群体拦截效率的关键。Gao等人提出的评估模型考虑了方位、速度和距离优势[6]。传统评估模型通常关注单无人机的个体价值，而较少考虑多无人机协同拦截的效果。Sun等人的评估模型不仅包含传统函数中的相对位置和速度，还引入了相对法向速度、法向加速度、机动性和多导弹协同[7]。Wang等人和Guo等人将协同拦截效果建模为几何覆盖问题[8,9]。文献中的评估模型多针对导弹，或未考虑固定翼无人机的转弯半径约束。对于固定翼无人机，由于各无人机机动能力不同且受转弯半径限制[10]，问题更具挑战性。因此，建立简洁而准确的评估模型至关重要。

评估模型建立后，需通过任务分配算法获得最优任务方案。许多学者提出了多种解决方法。多无人机任务分配是一个非确定性多项式困难（NP-hard）问题[11]。任务分配算法可分为集中式和分布式[12]。集中式算法通常比分布式算法拥有更多信息，能更高效地找到全局最优解[13]。在拦截场景中，无人机可从基地雷达系统获取所有任务信息，因此集中式算法在初始任务分配阶段具有优势。集中式任务分配方法包括优化算法和启发式算法[14,15]。启发式算法如蚁群算法[16]、粒子群算法[17]和遗传算法[18]依赖迭代获得最优解，但其效率受初始参数影响较大。经典优化方法包括混合整数规划，可清晰描述任务分配问题[19]。此外，基于图论的方法（如网络流模型）利用图论方法形式化任务和无人机的特性，建立匹配关系以生成有效任务分配方案[20]。然而，这些方法通常限于小规模模型。随着任务分配模型规模的增大，优化方法面临计算负担指数级增长的风险[21]。如何在短时间内为大规模任务分配问题提供解决方案是一个挑战。分层任务分配方案是一种重要研究方向，其核心是将复杂任务分配模型分解为多个小规模子模型[22-24]。

针对大规模群体拦截场景中的任务分配问题，本文提出了一种分层任务分配方案。主要贡献如下：  
- 设计了一种简洁而准确的评估模型，用于描述复杂的群体协同拦截场景。基于阿波罗尼斯圆和固定翼无人机动力学模型，该模型能准确描述多无人机的协同拦截效果，并指导任务分配问题的求解。  
- 在分层任务分配框架下，提出了一种适用于拦截场景的启发式模型分解方法。在模型分解阶段，基于分布特征和拦截优势将大规模无人机和目标有效划分；在任务分配阶段，建立适用于多无人机系统的网络流模型（NFO）求解各子模型的可行解。仿真结果表明，所提算法能在毫秒级内给出解决方案，且随着模型规模增大，运行时间进一步减少。

---

### 2. 问题描述  
典型的群体拦截场景如图1所示。假设无人机和目标均为固定翼无人机平台，且无人机数量多于目标（\(N_U \geq N_T\)）。目标最初由雷达系统检测，中央控制站根据相关信息对目标和无人机分组，评估攻防态势。随后，为每架无人机分配适合拦截的目标，并将多余无人机分配给高威胁目标以实现多对一拦截。根据任务分配方案，多无人机将在最优拦截点拦截多目标。

本文提出的分层任务分配框架如图2所示：  
1. 根据目标分布，采用特征相似性聚类将目标分解为多个子群。  
2. 基于聚类结果，确定各任务子群的空间位置和需求。  
3. 基于拦截优势的分配方法为聚类目标形成无人机编队。  
4. 将大规模任务问题分解为多个小规模子模型以降低计算负担。  
5. 基于分解结果，提出阿波罗尼斯圆评估函数，合理描述每架无人机的拦截效果。  
6. 在各子模型中，无人机将拦截效果信息广播至中央无人机，中央无人机基于网络流优化分配具体任务，并规划最优拦截点以最大化协同拦截成功率。  
7. 分配完成后，基于协同Dubins曲线的路径规划算法为无人机生成拦截路径。

---

### 3. 模型分解  
本文将任务分配问题建模为最小费用最大流的网络流模型 \(G = (V, E)\)。在模型分解阶段，假设大规模任务分配模型可分解为 \(N_S\) 个子模型，每个子模型的无人机集和任务集分别为 \(U_q = \{U_1, ..., U_{N_{U_q}}\}\) 和 \(T_q = \{T_1, ..., T_{N_{T_q}}\}\)。通过有效分解，分层任务分配模型的计算复杂度显著降低（公式4）。

#### 3.1 基于特征相似性聚类的目标分组  
目标状态集为 \(S(t) = \{s_1(t), s_2(t), ..., s_n(t)\}\)，其中 \(s_i\) 为第 \(i\) 个目标的状态。基于目标位置、速度和航向角计算相似性矩阵 \(M_d\)、\(M_v\) 和 \(M_\theta\)：  
1. 目标位置相似性：  
   \[
   M_d(d_{mn}) = 
   \begin{cases} 
   1, & d_{mn} \leq \alpha \\
   e^{-k(d_{mn}-\alpha)}, & d_{mn} > \alpha 
   \end{cases}
   \]  
   其中 \(\alpha\) 为允许的欧氏距离阈值。  
2. 目标速度相似性：  
   \[
   M_v(v_{mn}) = \frac{\alpha_2 - v_{mn}}{\alpha_2 - \alpha_1}, \quad \alpha_1 \leq v_{mn} \leq \alpha_2
   \]  
   航向角相似性 \(M_\theta(\theta_{mn})\) 的计算方法与速度相似性相同。

通过定义子群目标数量的上下限 \([b_l, b_u]\)，改进传统聚类算法（算法1），确保子群规模均衡。

#### 3.2 基于拦截优势的无人机分配  
拦截优势 \(D_{ij}\) 综合考虑速度优势 \(D_{vij}\)、距离优势 \(D_{dij}\) 和航向角优势 \(D_{\theta ij}\)：  
\[
D_{ij} = \left[\lambda_1 D_{vij}(v_i, v_{Tj}) + \lambda_2 D_{dij}(d_{rij})\right] \cdot D_{\theta ij}(\theta_i, \theta_{Tj})
\]  
基于拦截优势，采用拍卖算法（算法2）为子群分配无人机编队。

---

### 4. 评估模型与任务分配方法  
#### 4.1 基于阿波罗尼斯圆的评估模型  
拦截概率 \(p_{ij}\) 定义为无人机可拦截区域与目标可机动区域的比值：  
\[
p_{ij} = \frac{\theta_{ij}}{\theta_{Tj}}
\]  
目标威胁等级 \(W_j\) 由目标距离 \(W_1(d)\) 和机动性 \(W_2(v_T)\) 加权计算：  
\[
W_j = \lambda_3 W_1(d) + \lambda_4 W_2(v_T)
\]  
子模型的目标函数为最大化拦截效能：  
\[
\max I_q = \sum_{j}^{N_{Tq}} \sum_{i}^{N_{Uq}} P_j^0 \cdot x_{ij} p_{ij}
\]  
约束条件包括单无人机单目标、多无人机协同拦截等（公式25-28）。

#### 4.2 基于网络流模型的任务分配  
任务分配问题建模为带上下界的最小费用最大流问题（图7）。根据无人机与目标数量关系，设置不同的流量约束：  
1. \(N_U \geq 2N_T\)：目标顶点到汇点的边流量限制为 \([1, 2]\)。  
2. \(N_T \leq N_U < 2N_T\)：源点到无人机顶点的边流量限制为 \([1, 1]\)，目标顶点到汇点的边流量限制为 \([1, 2]\)。

#### 4.3 无人机拦截点设计  
拦截点位于以目标为中心、检测距离 \(L_0\) 为半径的圆上（公式29-31）。路径规划采用Dubins曲线，满足固定翼无人机的动态约束。

---

### 5. 仿真结果  
#### 5.1 分层任务分配方案的可行性  
在 \(N_T=30\)、\(N_U=40\) 的拦截场景中，目标被聚类为4个子群（图9）。任务分配结果（图10）显示，所有目标均被有效拦截，且拦截区域覆盖目标机动区域。阿波罗尼斯圆的动态变化（图12）验证了评估模型的合理性。

#### 5.2 算法运行时间分析  
与集中式网络流算法（NFO）和匈牙利算法（HA）相比，所提HTA-NFO方案在运行时间（表2）和解质量（表3）上均表现优异。例如，在200v100场景中，运行时间减少84.86%，解质量偏差小于10%。

---

### 6. 结论  
本文提出了一种适用于大规模群体拦截场景的分层任务分配方案，通过模型分解、评估模型优化和网络流求解，实现了高效任务分配。仿真表明，该方案能在毫秒级内生成可行解，且随着规模增大优势更显著。未来可进一步研究动态拦截策略和更复杂的协同机制。

---

**补充材料**：视频见 https://github.com/xnwu9/HTA_NFO（访问日期：2023年8月18日）。
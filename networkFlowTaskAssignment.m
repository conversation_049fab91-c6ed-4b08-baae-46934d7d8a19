function assignments = networkFlowTaskAssignment(UAVs, Targets, P)
% 基于网络流模型的任务分配算法，符合论文描述
% 输入:
%   UAVs: 无人机结构体数组
%   Targets: 目标结构体数组
%   P: 拦截概率矩阵，N_U × N_T
% 输出:
%   assignments: 任务分配结果，N_U × N_T的矩阵，1表示分配，0表示不分配

N_U = length(UAVs);
N_T = length(Targets);

% 计算威胁度
threats = zeros(N_T, 1);
for j = 1:N_T
    threats(j) = Targets(j).threat;
end

% 优化：将拦截概率转换为网络的代价时，增加高威胁目标的权重
% 这样高威胁目标更容易被分配到更多无人机
[sorted_threats, threat_indices] = sort(threats, 'descend');
threat_weights = ones(N_T, 1);
high_threat_count = ceil(0.5 * N_T); % 前50%视为高威胁目标
for j = 1:high_threat_count
    idx = threat_indices(j);
    threat_weights(idx) = 1.5; % 高威胁目标权重增加50%
end

% 将拦截概率转换为网络的代价（求负值，使最大概率转为最小代价）
% 并应用威胁度权重
C = zeros(N_U, N_T);
for i = 1:N_U
    for j = 1:N_T
        C(i, j) = -P(i, j) * threat_weights(j);
    end
end

% 初始化赋值矩阵
assignments = zeros(N_U, N_T);

% 计算有多少目标可以被分配多个无人机
% 目标：至少80%的目标获得多无人机拦截
multi_uav_target_count = ceil(0.8 * N_T);

% 检查无人机数量是否足够实现80%多无人机拦截
if N_U < N_T + multi_uav_target_count
    disp('警告：无人机数量不足以达到80%的多无人机拦截率');
    disp(['需要至少 ', num2str(N_T + multi_uav_target_count), ' 架无人机，当前有 ', num2str(N_U), ' 架']);
    multi_uav_target_count = N_U - N_T; % 调整为可能的最大值
    disp(['调整目标为 ', num2str(multi_uav_target_count), ' 个多无人机拦截目标 (', ...
          num2str(multi_uav_target_count/N_T*100), '%)']);
end

% 优化：分两阶段进行任务分配，保证高威胁目标优先获得多无人机拦截

% 第一阶段：确保每个目标至少分配一个无人机
assignments_phase1 = twoPhaseAssignment(C, threats, P, N_U, N_T, multi_uav_target_count);

% 如果整数规划方法成功，直接使用结果
if ~isempty(assignments_phase1)
    assignments = assignments_phase1;
else
    % 备用贪心算法
    disp('使用备用贪心算法');
    assignments = backupGreedyAssignment(P, threats, N_U, N_T, multi_uav_target_count);
end

% 计算并输出拦截率统计
targets_count = sum(assignments, 1);
single_uav_targets = sum(targets_count == 1);
multi_uav_targets = sum(targets_count > 1);
no_uav_targets = sum(targets_count == 0);

disp('======= 任务分配统计 =======');
disp(['单个无人机拦截的目标数量: ', num2str(single_uav_targets)]);
disp(['多个无人机拦截的目标数量: ', num2str(multi_uav_targets)]);
disp(['未分配无人机的目标数量: ', num2str(no_uav_targets)]);
disp(['目标分配率: ', num2str((single_uav_targets + multi_uav_targets)/N_T*100), '%']);
disp(['多无人机拦截率: ', num2str(multi_uav_targets/N_T*100), '%']);

end

% 两阶段分配函数：首先确保每个目标至少一个无人机，然后优化多无人机拦截
function assignments = twoPhaseAssignment(C, threats, P, N_U, N_T, multi_uav_target_count)
    % 初始化结果
    assignments = [];
    
    % 按威胁度对目标排序
    [~, threat_indices] = sort(threats, 'descend');
    
    try
        % 第一阶段：整数线性规划确保每个目标至少分配一个无人机
        % 决策变量x的维度为N_U * N_T
        f = reshape(C, [], 1);  % 将代价矩阵展开为列向量作为目标函数
        
        % 约束条件1：每个无人机最多拦截一个目标
        A_uav = zeros(N_U, N_U * N_T);
        for i = 1:N_U
            A_uav(i, ((i-1)*N_T+1):(i*N_T)) = 1;
        end
        
        % 约束条件2：每个目标至少被一个无人机拦截，最多被两个无人机拦截
        A_target_lb = zeros(N_T, N_U * N_T);  % 目标下界约束
        A_target_ub = zeros(N_T, N_U * N_T);  % 目标上界约束
        for j = 1:N_T
            for i = 1:N_U
                A_target_lb(j, (i-1)*N_T+j) = 1;
                A_target_ub(j, (i-1)*N_T+j) = 1;
            end
        end
        
        % 约束条件3：至少multi_uav_target_count个目标获得多无人机拦截
        % 创建多无人机拦截约束矩阵
        A_multi = zeros(1, N_U * N_T);
        for j = 1:N_T
            for i = 1:N_U
                A_multi(1, (i-1)*N_T+j) = 1;
            end
        end
        
        % 组合所有约束
        A = [A_uav; -A_target_lb; A_target_ub; -A_multi];
        b = [ones(N_U, 1); -ones(N_T, 1); 2*ones(N_T, 1); -(N_T + multi_uav_target_count)];
        
        % 整数约束
        intcon = 1:(N_U*N_T);
        lb = zeros(N_U*N_T, 1);
        ub = ones(N_U*N_T, 1);
        
        % 求解整数线性规划问题
        options = optimoptions('intlinprog', 'Display', 'off');
        [x, ~, exitflag] = intlinprog(f, intcon, A, b, [], [], lb, ub, options);
        
        % 检查是否找到解决方案
        if exitflag > 0
            % 将解重新整形为N_U × N_T的矩阵
            assignments = reshape(x, N_T, N_U)';
            disp('整数线性规划求解成功');
        else
            disp('整数线性规划未找到满足多无人机拦截率约束的解，尝试重新求解');
            % 降低多无人机拦截目标数量，再次尝试求解
            reduced_multi_count = floor(multi_uav_target_count * 0.9);
            if reduced_multi_count > 0
                % 修改约束条件并重新求解
                b(end) = -(N_T + reduced_multi_count);
                [x, ~, exitflag] = intlinprog(f, intcon, A, b, [], [], lb, ub, options);
                if exitflag > 0
                    assignments = reshape(x, N_T, N_U)';
                    disp(['使用降低的多无人机拦截目标数量(', num2str(reduced_multi_count), ')求解成功']);
                else
                    disp('降低要求后仍未找到解');
                end
            end
        end
    catch e
        disp(['优化错误: ', e.message]);
    end
end

% 备用贪心算法，确保达到多无人机拦截率目标
function assignments = backupGreedyAssignment(P, threats, N_U, N_T, multi_uav_target_count)
    % 初始化赋值矩阵
    assignments = zeros(N_U, N_T);
    
    % 按威胁度对目标排序
    [~, threat_order] = sort(threats, 'descend');
    
    % 第一轮：每个目标至少分配一个无人机
    C = -P;  % 将概率转为代价
    for j = threat_order'
        [~, best_uav_idx] = min(C(:, j));
        assignments(best_uav_idx, j) = 1;
        C(best_uav_idx, :) = Inf;  % 标记该无人机已使用
    end
    
    % 第二轮：为高威胁目标分配额外的无人机
    remaining_uavs = find(sum(assignments, 2) == 0);
    
    % 检查是否有足够的无人机完成多无人机拦截任务
    actual_multi_count = min(multi_uav_target_count, length(remaining_uavs));
    
    if ~isempty(remaining_uavs)
        % 优先给高威胁目标分配额外无人机
        for i = 1:actual_multi_count
            if i > length(threat_order)
                break;
            end
            j = threat_order(i);
            [~, best_uav_idx] = min(P(remaining_uavs, j));
            uav_idx = remaining_uavs(best_uav_idx);
            assignments(uav_idx, j) = 1;
            remaining_uavs(best_uav_idx) = [];
            if isempty(remaining_uavs)
                break;
            end
        end
    end
end 
function visualizeUAVAssignment(UAVs, Targets, target_clusters, UAV_teams, battlefield_size)
% 可视化无人机团队分配结果
% 输入:
%   UAVs: 无人机结构体数组
%   Targets: 目标结构体数组
%   target_clusters: 目标聚类结果
%   UAV_teams: 无人机团队分配结果
%   battlefield_size: 战场大小

figure;
hold on;

% 为每个聚类指定不同的颜色
colors = hsv(length(target_clusters));

% 为图例创建空句柄
h_target = [];
h_center = [];
h_uav = [];
h_unassigned = [];

% 绘制目标聚类
for i = 1:length(target_clusters)
    cluster = target_clusters{i};
    
    % 计算聚类中心
    center_x = 0;
    center_y = 0;
    for j = 1:length(cluster)
        target_id = cluster(j);
        center_x = center_x + Targets(target_id).x;
        center_y = center_y + Targets(target_id).y;
        
        % 绘制目标
        h = plot(Targets(target_id).x, Targets(target_id).y, 'o', 'MarkerSize', 8, ...
            'MarkerEdgeColor', colors(i,:), 'MarkerFaceColor', colors(i,:));
        
        if isempty(h_target)
            h_target = h;
        end
    end
    
    % 计算并绘制聚类中心
    center_x = center_x / length(cluster);
    center_y = center_y / length(cluster);
    h = plot(center_x, center_y, 'ks', 'MarkerSize', 12, 'MarkerFaceColor', colors(i,:));
    
    if isempty(h_center)
        h_center = h;
    end
    
    % 添加聚类标签
    text(center_x, center_y, ['Cluster ' num2str(i)], 'FontSize', 10, 'FontWeight', 'bold');
    
    % 绘制分配给该聚类的无人机
    team = UAV_teams{i};
    for j = 1:length(team)
        uav_id = team(j);
        h = plot(UAVs(uav_id).x, UAVs(uav_id).y, '^', 'MarkerSize', 8, ...
            'MarkerEdgeColor', 'black', 'MarkerFaceColor', colors(i,:));
        
        if isempty(h_uav)
            h_uav = h;
        end
        
        % 绘制从无人机到目标聚类中心的连线
        plot([UAVs(uav_id).x, center_x], [UAVs(uav_id).y, center_y], '--', ...
            'Color', colors(i,:), 'LineWidth', 0.5);
    end
end

% 绘制未分配的无人机
assigned = false(1, length(UAVs));
for i = 1:length(UAV_teams)
    assigned(UAV_teams{i}) = true;
end
unassigned = find(~assigned);
for i = 1:length(unassigned)
    uav_id = unassigned(i);
    h = plot(UAVs(uav_id).x, UAVs(uav_id).y, '^', 'MarkerSize', 8, ...
        'MarkerEdgeColor', 'black', 'MarkerFaceColor', [0.7 0.7 0.7]);
    
    if isempty(h_unassigned)
        h_unassigned = h;
    end
end

title('无人机团队分配结果');
% 使用句柄创建正确的图例
legend([h_target, h_center, h_uav, h_unassigned], '目标', '聚类中心', '已分配无人机', '未分配无人机');
grid on;
axis([0 battlefield_size 0 battlefield_size]);
hold off;

end 
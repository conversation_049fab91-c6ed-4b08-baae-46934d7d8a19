function results = parameterSensitivityAnalysis()
% 参数敏感性分析工具
% 分析不同参数对算法性能的影响

fprintf('=== 参数敏感性分析 ===\n');
fprintf('正在分析不同参数对算法性能的影响...\n\n');

%% 基础参数设置
base_params = struct();
base_params.N_U = 40;
base_params.N_T = 30;
base_params.battlefield_size = 10000;
base_params.v_u = 50;
base_params.v_t = 70;
base_params.L_0 = 2000;
base_params.alpha = 1000;
base_params.b_l = 3;
base_params.b_u = 12;
base_params.lambda = 0.1;
base_params.lambda1 = 0.7;
base_params.lambda2 = 0.3;

%% 参数变化范围定义
param_ranges = struct();
param_ranges.lambda = [0.05, 0.1, 0.15, 0.2, 0.25];  % 相似度阈值
param_ranges.b_u = [8, 10, 12, 15, 18];              % 聚类上界
param_ranges.lambda1 = [0.5, 0.6, 0.7, 0.8, 0.9];   % 速度优势权重
param_ranges.uav_ratio = [1.2, 1.3, 1.4, 1.5, 1.6]; % 无人机目标比

results = struct();

%% 1. 相似度阈值敏感性分析
fprintf('1. 分析相似度阈值(lambda)的影响...\n');
lambda_results = analyzeSingleParameter('lambda', param_ranges.lambda, base_params);
results.lambda_analysis = lambda_results;

%% 2. 聚类上界敏感性分析
fprintf('2. 分析聚类上界(b_u)的影响...\n');
bu_results = analyzeSingleParameter('b_u', param_ranges.b_u, base_params);
results.bu_analysis = bu_results;

%% 3. 速度权重敏感性分析
fprintf('3. 分析速度优势权重(lambda1)的影响...\n');
lambda1_results = analyzeSingleParameter('lambda1', param_ranges.lambda1, base_params);
results.lambda1_analysis = lambda1_results;

%% 4. 无人机目标比敏感性分析
fprintf('4. 分析无人机目标比的影响...\n');
ratio_results = analyzeSingleParameter('uav_ratio', param_ranges.uav_ratio, base_params);
results.ratio_analysis = ratio_results;

%% 生成敏感性分析报告
generateSensitivityReport(results);

%% 可视化结果
visualizeSensitivityResults(results);

end

function param_results = analyzeSingleParameter(param_name, param_values, base_params)
% 分析单个参数的敏感性

num_values = length(param_values);
param_results = struct();
param_results.param_name = param_name;
param_results.param_values = param_values;
param_results.scores = zeros(1, num_values);
param_results.assignment_rates = zeros(1, num_values);
param_results.multi_uav_rates = zeros(1, num_values);
param_results.execution_times = zeros(1, num_values);

for i = 1:num_values
    fprintf('  测试 %s = %.3f (%d/%d)\n', param_name, param_values(i), i, num_values);
    
    % 设置当前参数值
    current_params = base_params;
    if strcmp(param_name, 'uav_ratio')
        current_params.N_U = round(current_params.N_T * param_values(i));
    else
        current_params.(param_name) = param_values(i);
    end
    
    % 运行算法
    try
        [score, assignment_rate, multi_uav_rate, exec_time] = runAlgorithmWithParams(current_params);
        param_results.scores(i) = score;
        param_results.assignment_rates(i) = assignment_rate;
        param_results.multi_uav_rates(i) = multi_uav_rate;
        param_results.execution_times(i) = exec_time;
    catch e
        fprintf('    错误: %s\n', e.message);
        param_results.scores(i) = 0;
        param_results.assignment_rates(i) = 0;
        param_results.multi_uav_rates(i) = 0;
        param_results.execution_times(i) = inf;
    end
end

% 找到最优值
[~, best_idx] = max(param_results.scores);
param_results.best_value = param_values(best_idx);
param_results.best_score = param_results.scores(best_idx);

fprintf('  最优值: %s = %.3f (得分: %.1f)\n\n', param_name, param_results.best_value, param_results.best_score);

end

function [score, assignment_rate, multi_uav_rate, exec_time] = runAlgorithmWithParams(params)
% 使用指定参数运行算法

% 设置随机种子确保可重现性
rng(42);

% 创建简化的目标分布
Targets = createSimpleTargets(params.N_T, params.battlefield_size, params.v_t);
UAVs = createSimpleUAVs(params.N_U, params.battlefield_size, params.v_u);

tic;

% 计算相似度矩阵
[M_d, M_v, M_theta] = calculateSimilarityMatrices(Targets, params.alpha);

% 执行聚类
target_clusters = featureSimilarityClustering(M_d, M_v, M_theta, params.lambda, params.b_l, params.b_u, Targets);

% 计算拦截优势
D = calculateInterceptionAdvantage(UAVs, target_clusters, params.lambda1, params.lambda2, Targets);

% 分配无人机团队
UAV_teams = assignUAVs(D, target_clusters, params.N_U);

% 任务分配
all_assignments = cell(1, length(target_clusters));
for q = 1:length(target_clusters)
    current_UAVs = UAVs(UAV_teams{q});
    current_targets = Targets(target_clusters{q});
    P = calculateInterceptionProbability(current_UAVs, current_targets, params.v_u, params.v_t, params.L_0);
    all_assignments{q} = networkFlowTaskAssignment(current_UAVs, current_targets, P);
end

exec_time = toc;

% 计算性能指标
total_assigned = 0;
total_multi_uav = 0;
total_targets = params.N_T;

for q = 1:length(all_assignments)
    assignments = all_assignments{q};
    targets_count = sum(assignments, 1);
    total_assigned = total_assigned + sum(targets_count > 0);
    total_multi_uav = total_multi_uav + sum(targets_count > 1);
end

assignment_rate = total_assigned / total_targets;
multi_uav_rate = total_multi_uav / total_targets;

% 简化的综合评分
score = assignment_rate * 40 + multi_uav_rate * 30 + min(1, 1/exec_time) * 30;

rng('shuffle');

end

function Targets = createSimpleTargets(N_T, battlefield_size, v_t)
% 创建简化的目标分布

Targets = struct('x', {}, 'y', {}, 'v', {}, 'theta', {}, 'threat', {});

% 创建3个聚类
cluster_centers = [2000, 2000; 6000, 6000; 8000, 3000];
targets_per_cluster = floor(N_T / 3);
remaining = N_T - 3 * targets_per_cluster;

idx = 1;
for c = 1:3
    count = targets_per_cluster + (c == 3) * remaining;
    for i = 1:count
        angle = rand() * 2 * pi;
        radius = 300 + 200 * rand();
        Targets(idx).x = cluster_centers(c, 1) + radius * cos(angle);
        Targets(idx).y = cluster_centers(c, 2) + radius * sin(angle);
        Targets(idx).v = v_t * (0.9 + 0.2 * rand());
        Targets(idx).theta = rand() * 2 * pi;
        Targets(idx).threat = rand();
        idx = idx + 1;
    end
end

end

function UAVs = createSimpleUAVs(N_U, battlefield_size, v_u)
% 创建简化的无人机分布

UAVs = struct('x', {}, 'y', {}, 'v', {}, 'theta', {});

for i = 1:N_U
    UAVs(i).x = rand() * battlefield_size;
    UAVs(i).y = rand() * battlefield_size;
    UAVs(i).v = v_u * (0.9 + 0.2 * rand());
    UAVs(i).theta = rand() * 2 * pi;
end

end

function generateSensitivityReport(results)
% 生成敏感性分析报告

fprintf('\n=== 参数敏感性分析报告 ===\n');

fields = fieldnames(results);
for i = 1:length(fields)
    if contains(fields{i}, '_analysis')
        analysis = results.(fields{i});
        fprintf('\n%s 敏感性分析:\n', analysis.param_name);
        fprintf('  最优值: %.3f\n', analysis.best_value);
        fprintf('  最优得分: %.1f\n', analysis.best_score);
        
        % 计算敏感性指数（得分变化范围）
        score_range = max(analysis.scores) - min(analysis.scores);
        fprintf('  敏感性指数: %.1f (得分变化范围)\n', score_range);
        
        if score_range > 20
            sensitivity = '高';
        elseif score_range > 10
            sensitivity = '中';
        else
            sensitivity = '低';
        end
        fprintf('  敏感性等级: %s\n', sensitivity);
    end
end

end

function visualizeSensitivityResults(results)
% 可视化敏感性分析结果

figure('Position', [100, 100, 1200, 800]);

fields = fieldnames(results);
analysis_fields = fields(contains(fields, '_analysis'));
num_analyses = length(analysis_fields);

for i = 1:num_analyses
    analysis = results.(analysis_fields{i});
    
    subplot(2, 2, i);
    plot(analysis.param_values, analysis.scores, 'o-', 'LineWidth', 2, 'MarkerSize', 8);
    xlabel(analysis.param_name);
    ylabel('综合得分');
    title(sprintf('%s 敏感性分析', analysis.param_name));
    grid on;
    
    % 标记最优点
    [~, best_idx] = max(analysis.scores);
    hold on;
    plot(analysis.param_values(best_idx), analysis.scores(best_idx), 'r*', 'MarkerSize', 15);
    hold off;
end

sgtitle('参数敏感性分析结果');

end

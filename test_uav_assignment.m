%% 测试无人机分配算法
clear;
clc;

% 参数设置
N_U = 54;  % 无人机数量
N_T = 30;  % 目标数量
battlefield_size = 10000;
v_u = 50;
v_t = 70;
alpha = 1000;
b_l = 3;
b_u = 12;
lambda = 0.3;
lambda1 = 0.7;
lambda2 = 0.3;

% 初始化UAV
UAVs = struct('x', {}, 'y', {}, 'v', {}, 'theta', {});
for i = 1:N_U
    UAVs(i).x = rand() * battlefield_size;
    UAVs(i).y = rand() * battlefield_size;
    UAVs(i).v = v_u * (0.9 + rand() * 0.2);
    UAVs(i).theta = rand() * 2 * pi;
end

% 初始化目标
Targets = struct('x', {}, 'y', {}, 'v', {}, 'theta', {}, 'threat', {});
center_x = battlefield_size / 2;
center_y = battlefield_size / 2;

% 创建目标分布
for i = 1:N_T
    % 随机分布在战场中
    Targets(i).x = rand() * battlefield_size;
    Targets(i).y = rand() * battlefield_size;
    Targets(i).v = v_t * (0.8 + rand() * 0.4);
    Targets(i).theta = rand() * 2 * pi;
    Targets(i).threat = rand();
end

% 计算相似度矩阵
[M_d, M_v, M_theta] = calculateSimilarityMatrices(Targets, alpha);

% 执行聚类
target_clusters = featureSimilarityClustering(M_d, M_v, M_theta, lambda, b_l, b_u, Targets);

% 计算拦截优势矩阵
D = calculateInterceptionAdvantage(UAVs, target_clusters, lambda1, lambda2, Targets);

% 测试无人机分配
disp('开始测试无人机分配...');
UAV_teams = assignUAVs(D, target_clusters, N_U);

disp('测试完成');

%% 检查和改进可视化效果
% 专门检查Figure 2和其他图形的显示质量

clear;
clc;
close all;

fprintf('=== 检查当前可视化效果 ===\n');

%% 重新运行一个简化版本来检查图形
% 基本参数设置
N_U = 54;
N_T = 30;
battlefield_size = 10000;
v_u = 50;
v_t = 70;
L_0 = 2000;
alpha = 1000;
b_l = 3;
b_u = 12;
lambda = 0.7;
lambda1 = 0.7;
lambda2 = 0.3;
lambda3 = 0.6;
lambda4 = 0.4;
d_min = 3000;
d_max = 8000;

% 使用固定种子确保可重现
rng(42);

%% 初始化UAV（使用模式2：三基地部署）
UAVs = struct('x', {}, 'y', {}, 'v', {}, 'theta', {});

% 三个基地位置
base1_x = 2000; base1_y = 2000;
base2_x = 2000; base2_y = 6000;
base3_x = 5000; base3_y = 2000;

uavs_per_base = floor(N_U / 3);
remaining_uavs = N_U - 3 * uavs_per_base;
uav_idx = 1;

% 基地1部署
for i = 1:uavs_per_base
    angle = (i-1) * 2*pi / uavs_per_base;
    radius = 80 + 40 * (i-1) / uavs_per_base;
    UAVs(uav_idx).x = base1_x + radius * cos(angle);
    UAVs(uav_idx).y = base1_y + radius * sin(angle);
    UAVs(uav_idx).v = v_u;
    UAVs(uav_idx).theta = angle + pi/4;
    uav_idx = uav_idx + 1;
end

% 基地2部署
for i = 1:uavs_per_base
    angle = (i-1) * 2*pi / uavs_per_base;
    radius = 80 + 40 * (i-1) / uavs_per_base;
    UAVs(uav_idx).x = base2_x + radius * cos(angle);
    UAVs(uav_idx).y = base2_y + radius * sin(angle);
    UAVs(uav_idx).v = v_u;
    UAVs(uav_idx).theta = angle - pi/4;
    uav_idx = uav_idx + 1;
end

% 基地3部署
for i = 1:(uavs_per_base + remaining_uavs)
    angle = (i-1) * 2*pi / (uavs_per_base + remaining_uavs);
    radius = 80 + 60 * (i-1) / (uavs_per_base + remaining_uavs);
    UAVs(uav_idx).x = base3_x + radius * cos(angle);
    UAVs(uav_idx).y = base3_y + radius * sin(angle);
    UAVs(uav_idx).v = v_u;
    UAVs(uav_idx).theta = angle + pi/2;
    uav_idx = uav_idx + 1;
end

%% 初始化目标（创建符合论文图3的分布）
Targets = struct('x', {}, 'y', {}, 'v', {}, 'theta', {}, 'threat', {});

% 创建4个目标群组 + 2个孤立点
cluster_centers = [2500, 4000; 7500, 6000; 4500, 2500; 4500, 7000];
targets_per_cluster = [8, 7, 6, 7];  % 每个聚类的目标数量
remaining_targets = N_T - sum(targets_per_cluster);

center_x = battlefield_size / 2;
center_y = battlefield_size / 2;

idx = 1;
for c = 1:4
    for i = 1:targets_per_cluster(c)
        angle = (i-1) * 2*pi / targets_per_cluster(c) + 0.3*randn();
        radius = 300 + 200*rand();
        Targets(idx).x = cluster_centers(c, 1) + radius * cos(angle);
        Targets(idx).y = cluster_centers(c, 2) + radius * sin(angle);
        Targets(idx).v = v_t * (0.8 + 0.4*rand());
        Targets(idx).theta = angle + pi/4 + 0.5*randn();
        
        % 计算威胁度
        d = sqrt((Targets(idx).x - center_x)^2 + (Targets(idx).y - center_y)^2);
        W1 = calculateDistanceThreat(d, d_min, d_max);
        W2 = log(1 + Targets(idx).v^2);
        Targets(idx).threat = lambda3 * W1 + lambda4 * W2;
        
        idx = idx + 1;
    end
end

% 添加孤立点
for i = 1:remaining_targets
    Targets(idx).x = rand() * battlefield_size;
    Targets(idx).y = rand() * battlefield_size;
    Targets(idx).v = v_t * (0.5 + rand());
    Targets(idx).theta = rand() * 2*pi;
    
    d = sqrt((Targets(idx).x - center_x)^2 + (Targets(idx).y - center_y)^2);
    W1 = calculateDistanceThreat(d, d_min, d_max);
    W2 = log(1 + Targets(idx).v^2);
    Targets(idx).threat = lambda3 * W1 + lambda4 * W2;
    
    idx = idx + 1;
end

%% 执行聚类
fprintf('执行特征相似性聚类...\n');
[M_d, M_v, M_theta] = calculateSimilarityMatrices(Targets, alpha);
target_clusters = featureSimilarityClustering(M_d, M_v, M_theta, lambda, b_l, b_u, Targets);

%% 创建改进的Figure 2 - 聚类结果可视化
fprintf('生成改进的Figure 2...\n');

figure('Position', [100, 100, 1200, 900]);
set(gcf, 'Color', 'white');

% 设置更好的颜色方案
colors = [
    0.8500, 0.3250, 0.0980;  % 红橙色
    0.0000, 0.4470, 0.7410;  % 蓝色
    0.9290, 0.6940, 0.1250;  % 黄色
    0.4940, 0.1840, 0.5560;  % 紫色
    0.4660, 0.6740, 0.1880;  % 绿色
    0.3010, 0.7450, 0.9330;  % 青色
    0.6350, 0.0780, 0.1840;  % 深红色
];

hold on;

% 绘制战场边界
plot([0 battlefield_size battlefield_size 0 0], [0 0 battlefield_size battlefield_size 0], ...
     'k-', 'LineWidth', 2);

% 绘制每个聚类
for i = 1:length(target_clusters)
    cluster = target_clusters{i};
    cluster_x = [Targets(cluster).x];
    cluster_y = [Targets(cluster).y];
    
    color_idx = mod(i-1, size(colors, 1)) + 1;
    current_color = colors(color_idx, :);
    
    % 绘制聚类边界（凸包）
    if length(cluster) > 2
        try
            k = convhull(cluster_x, cluster_y);
            % 绘制填充区域
            h_fill = fill(cluster_x(k), cluster_y(k), current_color);
            set(h_fill, 'FaceAlpha', 0.15, 'EdgeColor', current_color, ...
                'EdgeAlpha', 0.8, 'LineWidth', 2, 'LineStyle', '--');
        catch
            % 如果点共线，跳过凸包绘制
        end
    end
    
    % 绘制目标点
    for j = 1:length(cluster)
        target_id = cluster(j);
        threat_size = 8 + Targets(target_id).threat * 12;  % 威胁度影响大小
        
        % 绘制目标
        h_target = plot(Targets(target_id).x, Targets(target_id).y, 'o', ...
            'MarkerSize', threat_size, 'MarkerEdgeColor', current_color, ...
            'MarkerFaceColor', current_color, 'LineWidth', 2);
        
        % 绘制速度向量
        arrow_length = 150;
        quiver(Targets(target_id).x, Targets(target_id).y, ...
            arrow_length*cos(Targets(target_id).theta), ...
            arrow_length*sin(Targets(target_id).theta), ...
            0, 'Color', current_color, 'LineWidth', 1.5, 'MaxHeadSize', 0.3);
        
        % 添加目标标签
        text(Targets(target_id).x + 200, Targets(target_id).y + 200, ...
            sprintf('T%d', target_id), 'FontSize', 8, 'Color', current_color, ...
            'FontWeight', 'bold', 'BackgroundColor', 'white', 'EdgeColor', current_color);
    end
    
    % 标记聚类中心和编号
    center_x_cluster = mean(cluster_x);
    center_y_cluster = mean(cluster_y);
    plot(center_x_cluster, center_y_cluster, 'k+', 'MarkerSize', 20, 'LineWidth', 4);
    text(center_x_cluster + 300, center_y_cluster + 300, ...
         sprintf('聚类%d\n(%d个目标)', i, length(cluster)), ...
         'FontSize', 12, 'FontWeight', 'bold', 'Color', 'black', ...
         'BackgroundColor', 'white', 'EdgeColor', 'black', ...
         'HorizontalAlignment', 'center');
end

% 设置图形属性
title('Figure 2: 特征相似性聚类结果', 'FontSize', 16, 'FontWeight', 'bold');
xlabel('X坐标 (m)', 'FontSize', 14);
ylabel('Y坐标 (m)', 'FontSize', 14);
grid on;
grid minor;
axis equal;
axis([0 battlefield_size 0 battlefield_size]);

% 添加图例
legend_elements = {};
legend_labels = {};
for i = 1:min(length(target_clusters), 5)  % 最多显示5个聚类的图例
    color_idx = mod(i-1, size(colors, 1)) + 1;
    current_color = colors(color_idx, :);
    legend_elements{end+1} = plot(NaN, NaN, 'o', 'MarkerSize', 10, ...
        'MarkerFaceColor', current_color, 'MarkerEdgeColor', current_color);
    legend_labels{end+1} = sprintf('聚类%d (%d个目标)', i, length(target_clusters{i}));
end

if ~isempty(legend_elements)
    legend([legend_elements{:}], legend_labels, 'Location', 'northeast', ...
           'FontSize', 10, 'Box', 'on');
end

% 添加统计信息文本框
stats_text = sprintf(['聚类统计信息:\n' ...
                     '• 聚类数量: %d\n' ...
                     '• 目标总数: %d\n' ...
                     '• 平均聚类大小: %.1f\n' ...
                     '• 聚类大小范围: %d-%d'], ...
                     length(target_clusters), N_T, ...
                     mean(cellfun(@length, target_clusters)), ...
                     min(cellfun(@length, target_clusters)), ...
                     max(cellfun(@length, target_clusters)));

annotation('textbox', [0.02, 0.02, 0.25, 0.15], 'String', stats_text, ...
           'FontSize', 10, 'BackgroundColor', 'white', 'EdgeColor', 'black', ...
           'FitBoxToText', 'on');

hold off;

%% 保存图形
fprintf('保存改进的Figure 2...\n');
saveas(gcf, 'Figure2_Improved_Clustering.png');
saveas(gcf, 'Figure2_Improved_Clustering.fig');

%% 输出分析
fprintf('\n=== Figure 2 改进分析 ===\n');
fprintf('聚类数量: %d\n', length(target_clusters));
cluster_sizes = cellfun(@length, target_clusters);
fprintf('聚类大小: [%s]\n', num2str(cluster_sizes));
fprintf('聚类均衡度: %.3f (越小越好)\n', std(cluster_sizes)/mean(cluster_sizes));

% 检查聚类质量
total_intra_distance = 0;
for i = 1:length(target_clusters)
    cluster = target_clusters{i};
    if length(cluster) > 1
        cluster_x = [Targets(cluster).x];
        cluster_y = [Targets(cluster).y];
        center_x = mean(cluster_x);
        center_y = mean(cluster_y);
        distances = sqrt((cluster_x - center_x).^2 + (cluster_y - center_y).^2);
        avg_distance = mean(distances);
        total_intra_distance = total_intra_distance + avg_distance;
        fprintf('聚类%d内部平均距离: %.1f m\n', i, avg_distance);
    end
end

fprintf('总体聚类紧密度: %.1f m\n', total_intra_distance / length(target_clusters));

fprintf('\n✓ Figure 2 已改进并保存\n');
fprintf('文件保存为: Figure2_Improved_Clustering.png 和 .fig\n');

rng('shuffle');

function W1 = calculateDistanceThreat(d, d_min, d_max)
% 计算基于距离的威胁度
if d <= d_min
    W1 = 1;
elseif d >= d_max
    W1 = 0;
else
    W1 = (d_max - d) / (d_max - d_min);
end
end

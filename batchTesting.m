function results = batchTesting()
% 批量测试工具 - 测试不同场景下的算法性能
% 输出: results - 包含所有测试结果的结构体

fprintf('=== 批量测试工具 ===\n');
fprintf('正在进行多场景批量测试...\n\n');

%% 定义测试场景
scenarios = defineTestScenarios();
num_scenarios = length(scenarios);

%% 初始化结果存储
results = struct();
results.scenarios = scenarios;
results.performance = cell(1, num_scenarios);
results.summary = struct();

%% 执行批量测试
fprintf('开始执行 %d 个测试场景:\n', num_scenarios);

for i = 1:num_scenarios
    scenario = scenarios{i};
    fprintf('\n--- 场景 %d: %s ---\n', i, scenario.name);
    
    try
        % 运行单个场景测试
        scenario_result = runSingleScenario(scenario);
        results.performance{i} = scenario_result;
        
        fprintf('✓ 场景 %d 完成 - 得分: %.1f\n', i, scenario_result.overall.score);
        
    catch e
        fprintf('✗ 场景 %d 失败: %s\n', i, e.message);
        results.performance{i} = struct('error', e.message, 'overall', struct('score', 0));
    end
end

%% 生成汇总报告
results.summary = generateBatchSummary(results);

%% 可视化批量测试结果
visualizeBatchResults(results);

%% 保存结果
saveResults(results);

fprintf('\n=== 批量测试完成 ===\n');
fprintf('详细结果已保存到 batch_test_results.mat\n');

end

function scenarios = defineTestScenarios()
% 定义测试场景

scenarios = {};

% 场景1: 小规模场景
scenarios{1} = struct(...
    'name', '小规模场景', ...
    'N_U', 20, 'N_T', 15, ...
    'UAV_mode', 2, ...
    'description', '测试小规模场景下的算法性能');

% 场景2: 中等规模场景
scenarios{2} = struct(...
    'name', '中等规模场景', ...
    'N_U', 40, 'N_T', 30, ...
    'UAV_mode', 2, ...
    'description', '标准测试场景');

% 场景3: 大规模场景
scenarios{3} = struct(...
    'name', '大规模场景', ...
    'N_U', 80, 'N_T', 60, ...
    'UAV_mode', 2, ...
    'description', '测试大规模场景下的算法性能');

% 场景4: 无人机数量不足
scenarios{4} = struct(...
    'name', '资源受限场景', ...
    'N_U', 35, 'N_T', 30, ...
    'UAV_mode', 2, ...
    'description', '测试无人机数量相对不足的情况');

% 场景5: 无人机数量充足
scenarios{5} = struct(...
    'name', '资源充足场景', ...
    'N_U', 60, 'N_T', 30, ...
    'UAV_mode', 2, ...
    'description', '测试无人机数量充足的情况');

% 场景6: 随机分布测试
scenarios{6} = struct(...
    'name', '随机分布场景', ...
    'N_U', 40, 'N_T', 30, ...
    'UAV_mode', 1, ...
    'description', '测试随机分布下的算法鲁棒性');

% 场景7: 高度集中测试
scenarios{7} = struct(...
    'name', '集中部署场景', ...
    'N_U', 40, 'N_T', 30, ...
    'UAV_mode', 3, ...
    'description', '测试高度集中部署的效果');

end

function result = runSingleScenario(scenario)
% 运行单个测试场景

% 设置随机种子
rng(42);

%% 基本参数设置
battlefield_size = 10000;
v_u = 50;
v_t = 70;
L_0 = 2000;
alpha = 1000;
b_l = 3;
b_u = 12;
lambda = 0.1;
lambda1 = 0.7;
lambda2 = 0.3;
lambda3 = 0.6;
lambda4 = 0.4;
d_min = 3000;
d_max = 8000;

%% 初始化UAV和目标
UAVs = initializeUAVs(scenario.N_U, scenario.UAV_mode, battlefield_size, v_u);
Targets = initializeTargets(scenario.N_T, battlefield_size, v_t, lambda3, lambda4, d_min, d_max);

%% 记录开始时间
tic;

%% 执行算法
% 步骤1: 特征相似性聚类
[M_d, M_v, M_theta] = calculateSimilarityMatrices(Targets, alpha);
target_clusters = featureSimilarityClustering(M_d, M_v, M_theta, lambda, b_l, b_u, Targets);

% 步骤2: 基于拦截优势的无人机分配
D = calculateInterceptionAdvantage(UAVs, target_clusters, lambda1, lambda2, Targets);
UAV_teams = assignUAVs(D, target_clusters, scenario.N_U);

% 步骤3: 基于网络流模型的任务分配
num_clusters = length(target_clusters);
all_assignments = cell(1, num_clusters);

for q = 1:num_clusters
    current_UAVs = UAVs(UAV_teams{q});
    current_targets = Targets(target_clusters{q});
    P = calculateInterceptionProbability(current_UAVs, current_targets, v_u, v_t, L_0);
    all_assignments{q} = networkFlowTaskAssignment(current_UAVs, current_targets, P);
end

%% 记录执行时间
execution_time = toc;

%% 性能分析
result = performanceAnalysis(target_clusters, UAV_teams, all_assignments, UAVs, Targets, execution_time);
result.scenario = scenario;

% 重置随机种子
rng('shuffle');

end

function UAVs = initializeUAVs(N_U, UAV_mode, battlefield_size, v_u)
% 初始化无人机

UAVs = struct('x', {}, 'y', {}, 'v', {}, 'theta', {});

if UAV_mode == 1
    % 随机生成
    for i = 1:N_U
        UAVs(i).x = rand() * battlefield_size;
        UAVs(i).y = rand() * battlefield_size;
        UAVs(i).v = v_u * (0.9 + rand() * 0.2);
        UAVs(i).theta = rand() * 2 * pi;
    end
    
elseif UAV_mode == 2
    % 三基地部署
    base1_x = 2000; base1_y = 2000;
    base2_x = 2000; base2_y = 6000;
    base3_x = 5000; base3_y = 2000;
    
    uavs_per_base = floor(N_U / 3);
    remaining_uavs = N_U - 3 * uavs_per_base;
    uav_idx = 1;
    
    % 基地1
    for i = 1:uavs_per_base
        angle = (i-1) * 2*pi / uavs_per_base;
        radius = 80 + 40 * (i-1) / uavs_per_base;
        UAVs(uav_idx).x = base1_x + radius * cos(angle);
        UAVs(uav_idx).y = base1_y + radius * sin(angle);
        UAVs(uav_idx).v = v_u;
        UAVs(uav_idx).theta = angle + pi/4;
        uav_idx = uav_idx + 1;
    end
    
    % 基地2
    for i = 1:uavs_per_base
        angle = (i-1) * 2*pi / uavs_per_base;
        radius = 80 + 40 * (i-1) / uavs_per_base;
        UAVs(uav_idx).x = base2_x + radius * cos(angle);
        UAVs(uav_idx).y = base2_y + radius * sin(angle);
        UAVs(uav_idx).v = v_u;
        UAVs(uav_idx).theta = angle - pi/4;
        uav_idx = uav_idx + 1;
    end
    
    % 基地3
    for i = 1:(uavs_per_base + remaining_uavs)
        angle = (i-1) * 2*pi / (uavs_per_base + remaining_uavs);
        radius = 80 + 60 * (i-1) / (uavs_per_base + remaining_uavs);
        UAVs(uav_idx).x = base3_x + radius * cos(angle);
        UAVs(uav_idx).y = base3_y + radius * sin(angle);
        UAVs(uav_idx).v = v_u;
        UAVs(uav_idx).theta = angle + pi/2;
        uav_idx = uav_idx + 1;
    end
    
elseif UAV_mode == 3
    % 高度集中
    center_x = battlefield_size * 0.3;
    center_y = battlefield_size * 0.4;
    
    for i = 1:N_U
        spiral_angle = i * pi/3;
        spiral_radius = 50 + i * 12;
        UAVs(i).x = center_x + spiral_radius * cos(spiral_angle);
        UAVs(i).y = center_y + spiral_radius * sin(spiral_angle);
        UAVs(i).v = v_u * (0.95 + 0.1 * sin(spiral_angle));
        UAVs(i).theta = spiral_angle + pi/2;
    end
end

end

function Targets = initializeTargets(N_T, battlefield_size, v_t, lambda3, lambda4, d_min, d_max)
% 初始化目标（简化版本）

Targets = struct('x', {}, 'y', {}, 'v', {}, 'theta', {}, 'threat', {});

% 创建4个目标群组
cluster_centers = [2500, 2500; 7000, 7000; 8500, 3000; 3000, 8000];
targets_per_cluster = floor(N_T / 4);
remaining = N_T - 4 * targets_per_cluster;

center_x = battlefield_size / 2;
center_y = battlefield_size / 2;

idx = 1;
for c = 1:4
    count = targets_per_cluster + (c == 4) * remaining;
    for i = 1:count
        angle = rand() * 2 * pi;
        radius = 200 + 300 * rand();
        Targets(idx).x = cluster_centers(c, 1) + radius * cos(angle);
        Targets(idx).y = cluster_centers(c, 2) + radius * sin(angle);
        Targets(idx).v = v_t * (0.9 + 0.2 * rand());
        Targets(idx).theta = rand() * 2 * pi;
        
        % 计算威胁度
        d = sqrt((Targets(idx).x - center_x)^2 + (Targets(idx).y - center_y)^2);
        W1 = calculateDistanceThreat(d, d_min, d_max);
        W2 = log(1 + Targets(idx).v^2);
        Targets(idx).threat = lambda3 * W1 + lambda4 * W2;
        
        idx = idx + 1;
    end
end

end

function summary = generateBatchSummary(results)
% 生成批量测试汇总

num_scenarios = length(results.scenarios);
summary = struct();

% 提取所有得分
scores = zeros(1, num_scenarios);
assignment_rates = zeros(1, num_scenarios);
multi_uav_rates = zeros(1, num_scenarios);
execution_times = zeros(1, num_scenarios);

for i = 1:num_scenarios
    if isfield(results.performance{i}, 'overall')
        scores(i) = results.performance{i}.overall.score;
        assignment_rates(i) = results.performance{i}.assignment.assignment_rate;
        multi_uav_rates(i) = results.performance{i}.assignment.multi_uav_rate;
        execution_times(i) = results.performance{i}.basic.execution_time;
    end
end

% 计算统计指标
summary.avg_score = mean(scores);
summary.std_score = std(scores);
summary.min_score = min(scores);
summary.max_score = max(scores);
summary.avg_assignment_rate = mean(assignment_rates);
summary.avg_multi_uav_rate = mean(multi_uav_rates);
summary.avg_execution_time = mean(execution_times);

% 找到最佳和最差场景
[~, best_idx] = max(scores);
[~, worst_idx] = min(scores);
summary.best_scenario = results.scenarios{best_idx}.name;
summary.worst_scenario = results.scenarios{worst_idx}.name;

fprintf('\n=== 批量测试汇总报告 ===\n');
fprintf('测试场景数量: %d\n', num_scenarios);
fprintf('平均得分: %.1f ± %.1f\n', summary.avg_score, summary.std_score);
fprintf('得分范围: %.1f - %.1f\n', summary.min_score, summary.max_score);
fprintf('平均分配率: %.2f%%\n', summary.avg_assignment_rate * 100);
fprintf('平均多机协同率: %.2f%%\n', summary.avg_multi_uav_rate * 100);
fprintf('平均执行时间: %.3f 秒\n', summary.avg_execution_time);
fprintf('最佳场景: %s (得分: %.1f)\n', summary.best_scenario, summary.max_score);
fprintf('最差场景: %s (得分: %.1f)\n', summary.worst_scenario, summary.min_score);

end

function visualizeBatchResults(results)
% 可视化批量测试结果

num_scenarios = length(results.scenarios);
scenario_names = cell(1, num_scenarios);
scores = zeros(1, num_scenarios);

for i = 1:num_scenarios
    scenario_names{i} = results.scenarios{i}.name;
    if isfield(results.performance{i}, 'overall')
        scores(i) = results.performance{i}.overall.score;
    end
end

figure('Position', [100, 100, 1000, 600]);
bar(scores);
set(gca, 'XTickLabel', scenario_names);
xtickangle(45);
ylabel('综合得分');
title('批量测试结果对比');
grid on;

% 添加得分标签
for i = 1:num_scenarios
    text(i, scores(i) + 1, sprintf('%.1f', scores(i)), 'HorizontalAlignment', 'center');
end

end

function saveResults(results)
% 保存测试结果

filename = sprintf('batch_test_results_%s.mat', datestr(now, 'yyyymmdd_HHMMSS'));
save(filename, 'results');

end

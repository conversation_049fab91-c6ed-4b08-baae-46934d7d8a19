function enhancedVisualization(UAVs, Targets, target_clusters, UAV_teams, all_assignments, interception_points, performance_results)
% 增强可视化模块 - 提供多种高级可视化功能
% 输入:
%   UAVs: 无人机结构体数组
%   Targets: 目标结构体数组
%   target_clusters: 目标聚类结果
%   UAV_teams: 无人机团队分配结果
%   all_assignments: 任务分配结果
%   interception_points: 拦截点
%   performance_results: 性能分析结果

fprintf('=== 生成增强可视化图表 ===\n');

%% 1. 综合态势图
createComprehensiveSituationMap(UAVs, Targets, target_clusters, UAV_teams, all_assignments, interception_points);

%% 2. 性能指标仪表盘
createPerformanceDashboard(performance_results);

%% 3. 聚类分析图
createClusterAnalysisPlot(Targets, target_clusters);

%% 4. 任务分配网络图
createTaskAssignmentNetwork(UAVs, Targets, target_clusters, UAV_teams, all_assignments);

%% 5. 威胁分布热力图
createThreatHeatmap(Targets);

fprintf('✓ 所有增强可视化图表已生成\n');

end

function createComprehensiveSituationMap(UAVs, Targets, target_clusters, UAV_teams, all_assignments, interception_points)
% 创建综合态势图

figure('Position', [50, 50, 1400, 900]);

% 设置颜色方案
colors = lines(length(target_clusters));
battlefield_size = 10000;

hold on;

% 绘制战场网格
for i = 0:1000:battlefield_size
    plot([i i], [0 battlefield_size], 'k:', 'LineWidth', 0.5, 'Color', [0.7 0.7 0.7]);
    plot([0 battlefield_size], [i i], 'k:', 'LineWidth', 0.5, 'Color', [0.7 0.7 0.7]);
end

% 绘制目标和聚类
for i = 1:length(target_clusters)
    cluster = target_clusters{i};
    
    % 绘制聚类边界
    if length(cluster) > 2
        cluster_x = [Targets(cluster).x];
        cluster_y = [Targets(cluster).y];
        k = convhull(cluster_x, cluster_y);
        plot(cluster_x(k), cluster_y(k), '--', 'Color', colors(i,:), 'LineWidth', 2);
        % 使用patch来创建半透明填充
        h_fill = fill(cluster_x(k), cluster_y(k), colors(i,:));
        set(h_fill, 'FaceAlpha', 0.1, 'EdgeAlpha', 0.3);
    end
    
    % 绘制目标
    for j = 1:length(cluster)
        target_id = cluster(j);
        threat_size = 6 + Targets(target_id).threat * 8;  % 威胁度影响大小
        
        plot(Targets(target_id).x, Targets(target_id).y, 'o', ...
            'MarkerSize', threat_size, 'MarkerEdgeColor', colors(i,:), ...
            'MarkerFaceColor', colors(i,:), 'LineWidth', 2);
        
        % 绘制速度向量
        quiver(Targets(target_id).x, Targets(target_id).y, ...
            Targets(target_id).v*cos(Targets(target_id).theta), ...
            Targets(target_id).v*sin(Targets(target_id).theta), ...
            0.5, 'Color', colors(i,:), 'LineWidth', 1.5);
        
        % 添加目标标签
        text(Targets(target_id).x + 150, Targets(target_id).y + 150, ...
            sprintf('T%d(%.2f)', target_id, Targets(target_id).threat), ...
            'FontSize', 8, 'Color', colors(i,:), 'FontWeight', 'bold');
    end
end

% 绘制无人机和任务分配
for i = 1:length(target_clusters)
    team = UAV_teams{i};
    assignments = all_assignments{i};
    
    % 绘制无人机
    for j = 1:length(team)
        uav_id = team(j);
        
        % 检查该无人机是否有任务分配
        has_assignment = sum(assignments(j, :)) > 0;
        if has_assignment
            marker_style = '^';
            face_color = colors(i,:);
            edge_color = 'black';
            marker_size = 10;
        else
            marker_style = '^';
            face_color = 'white';
            edge_color = colors(i,:);
            marker_size = 8;
        end
        
        plot(UAVs(uav_id).x, UAVs(uav_id).y, marker_style, ...
            'MarkerSize', marker_size, 'MarkerEdgeColor', edge_color, ...
            'MarkerFaceColor', face_color, 'LineWidth', 2);
        
        % 绘制速度向量
        quiver(UAVs(uav_id).x, UAVs(uav_id).y, ...
            UAVs(uav_id).v*cos(UAVs(uav_id).theta), ...
            UAVs(uav_id).v*sin(UAVs(uav_id).theta), ...
            0.3, 'Color', 'blue', 'LineWidth', 1);
        
        % 添加无人机标签
        text(UAVs(uav_id).x + 100, UAVs(uav_id).y - 200, ...
            sprintf('U%d', uav_id), 'FontSize', 8, 'Color', 'blue');
    end
end

% 绘制拦截点和连线
for i = 1:length(interception_points)
    i_points = interception_points{i};
    for j = 1:length(i_points)
        % 绘制拦截点
        plot(i_points(j).x, i_points(j).y, 'x', 'MarkerSize', 12, ...
            'MarkerEdgeColor', 'red', 'LineWidth', 3);
        
        % 绘制连线
        uav_id = i_points(j).uav_id;
        target_id = i_points(j).target_id;
        
        % 无人机到拦截点
        plot([UAVs(uav_id).x, i_points(j).x], [UAVs(uav_id).y, i_points(j).y], ...
            '--', 'Color', 'blue', 'LineWidth', 2);
        
        % 目标到拦截点
        plot([Targets(target_id).x, i_points(j).x], [Targets(target_id).y, i_points(j).y], ...
            ':', 'Color', 'red', 'LineWidth', 2);
    end
end

% 设置图形属性
title('综合态势图', 'FontSize', 16, 'FontWeight', 'bold');
xlabel('X坐标 (m)', 'FontSize', 12);
ylabel('Y坐标 (m)', 'FontSize', 12);
axis([0 battlefield_size 0 battlefield_size]);
grid on;
axis equal;

% 创建图例
legend_elements = {
    plot(NaN, NaN, '^', 'MarkerSize', 10, 'MarkerFaceColor', 'blue', 'MarkerEdgeColor', 'black'), '已分配无人机';
    plot(NaN, NaN, '^', 'MarkerSize', 8, 'MarkerFaceColor', 'white', 'MarkerEdgeColor', 'blue'), '未分配无人机';
    plot(NaN, NaN, 'o', 'MarkerSize', 8, 'MarkerFaceColor', 'red', 'MarkerEdgeColor', 'red'), '目标';
    plot(NaN, NaN, 'x', 'MarkerSize', 12, 'MarkerEdgeColor', 'red', 'LineWidth', 3), '拦截点';
    plot(NaN, NaN, '--', 'Color', 'blue', 'LineWidth', 2), '无人机路径';
    plot(NaN, NaN, ':', 'Color', 'red', 'LineWidth', 2), '目标路径'
};

legend([legend_elements{:,1}], legend_elements(:,2), 'Location', 'best');
hold off;

end

function createPerformanceDashboard(performance_results)
% 创建性能指标仪表盘

figure('Position', [100, 100, 1200, 800]);

% 子图1: 综合得分仪表
subplot(2, 3, 1);
score = performance_results.overall.score;
theta = linspace(0, 2*pi, 100);
r_outer = 1;
r_inner = 0.7;

% 绘制仪表盘背景
fill(r_outer*cos(theta), r_outer*sin(theta), [0.9 0.9 0.9]);
hold on;
fill(r_inner*cos(theta), r_inner*sin(theta), 'white');

% 绘制得分指针
score_angle = (score/100) * 2*pi - pi/2;
plot([0, 0.8*cos(score_angle)], [0, 0.8*sin(score_angle)], 'r-', 'LineWidth', 4);

% 添加刻度
for i = 0:20:100
    angle = (i/100) * 2*pi - pi/2;
    plot([0.9*cos(angle), cos(angle)], [0.9*sin(angle), sin(angle)], 'k-', 'LineWidth', 1);
    text(1.1*cos(angle), 1.1*sin(angle), num2str(i), 'HorizontalAlignment', 'center');
end

title(sprintf('综合得分: %.1f', score), 'FontSize', 14, 'FontWeight', 'bold');
axis equal; axis off;
hold off;

% 子图2: 分配率条形图
subplot(2, 3, 2);
rates = [performance_results.assignment.assignment_rate, ...
         performance_results.assignment.multi_uav_rate, ...
         performance_results.assignment.single_uav_rate] * 100;
bar_colors = [0.2 0.7 0.2; 0.7 0.2 0.2; 0.2 0.2 0.7];
b = bar(rates);
b.FaceColor = 'flat';
b.CData = bar_colors;
set(gca, 'XTickLabel', {'总分配率', '多机协同', '单机拦截'});
ylabel('百分比 (%)');
title('任务分配统计', 'FontWeight', 'bold');
grid on;

% 子图3: 资源利用率饼图
subplot(2, 3, 3);
utilization = performance_results.resource.uav_utilization * 100;
unused = 100 - utilization;
pie([utilization, unused], {'已使用', '未使用'});
title(sprintf('无人机利用率: %.1f%%', utilization), 'FontWeight', 'bold');

% 子图4: 聚类质量分析
subplot(2, 3, 4);
cluster_sizes = performance_results.clustering.cluster_sizes;
bar(cluster_sizes);
xlabel('聚类编号');
ylabel('聚类大小');
title('聚类大小分布', 'FontWeight', 'bold');
grid on;

% 子图5: 威胁覆盖分析
subplot(2, 3, 5);
threat_coverage = [performance_results.threat.high_threat_coverage, ...
                   1 - performance_results.threat.high_threat_coverage] * 100;
pie(threat_coverage, {'已覆盖', '未覆盖'});
title('高威胁目标覆盖率', 'FontWeight', 'bold');

% 子图6: 性能等级显示
subplot(2, 3, 6);
grade_text = performance_results.overall.grade;
text(0.5, 0.5, grade_text, 'FontSize', 20, 'FontWeight', 'bold', ...
     'HorizontalAlignment', 'center', 'VerticalAlignment', 'middle');
title('性能等级', 'FontWeight', 'bold');
axis off;

sgtitle('算法性能仪表盘', 'FontSize', 16, 'FontWeight', 'bold');

end

function createClusterAnalysisPlot(Targets, target_clusters)
% 创建聚类分析图

figure('Position', [200, 200, 1000, 700]);

% 子图1: 聚类分布
subplot(2, 2, 1);
colors = lines(length(target_clusters));
hold on;

for i = 1:length(target_clusters)
    cluster = target_clusters{i};
    cluster_x = [Targets(cluster).x];
    cluster_y = [Targets(cluster).y];
    
    scatter(cluster_x, cluster_y, 100, colors(i,:), 'filled');
    
    % 计算并绘制聚类中心
    center_x = mean(cluster_x);
    center_y = mean(cluster_y);
    plot(center_x, center_y, 'k+', 'MarkerSize', 15, 'LineWidth', 3);
    
    text(center_x + 200, center_y + 200, sprintf('C%d', i), ...
         'FontSize', 12, 'FontWeight', 'bold');
end

title('目标聚类分布');
xlabel('X坐标 (m)');
ylabel('Y坐标 (m)');
grid on;
hold off;

% 子图2: 聚类大小分布
subplot(2, 2, 2);
cluster_sizes = cellfun(@length, target_clusters);
bar(cluster_sizes, 'FaceColor', [0.3 0.6 0.9]);
xlabel('聚类编号');
ylabel('聚类大小');
title('聚类大小分布');
grid on;

% 子图3: 聚类内距离分析
subplot(2, 2, 3);
intra_distances = zeros(1, length(target_clusters));
for i = 1:length(target_clusters)
    cluster = target_clusters{i};
    if length(cluster) > 1
        cluster_x = [Targets(cluster).x];
        cluster_y = [Targets(cluster).y];
        center_x = mean(cluster_x);
        center_y = mean(cluster_y);
        distances = sqrt((cluster_x - center_x).^2 + (cluster_y - center_y).^2);
        intra_distances(i) = mean(distances);
    end
end

bar(intra_distances, 'FaceColor', [0.9 0.6 0.3]);
xlabel('聚类编号');
ylabel('平均内部距离 (m)');
title('聚类紧密度分析');
grid on;

% 子图4: 威胁分布
subplot(2, 2, 4);
all_threats = [Targets.threat];
histogram(all_threats, 10, 'FaceColor', [0.6 0.3 0.9]);
xlabel('威胁等级');
ylabel('目标数量');
title('威胁等级分布');
grid on;

sgtitle('聚类质量分析', 'FontSize', 14, 'FontWeight', 'bold');

end

function createTaskAssignmentNetwork(UAVs, Targets, target_clusters, UAV_teams, all_assignments)
% 创建任务分配网络图

figure('Position', [300, 300, 1000, 700]);

hold on;

% 绘制网络连接
for q = 1:length(target_clusters)
    assignments = all_assignments{q};
    team = UAV_teams{q};
    cluster = target_clusters{q};
    
    [uav_indices, target_indices] = find(assignments);
    
    for k = 1:length(uav_indices)
        uav_id = team(uav_indices(k));
        target_id = cluster(target_indices(k));
        
        % 绘制连接线，线宽表示分配强度
        plot([UAVs(uav_id).x, Targets(target_id).x], ...
             [UAVs(uav_id).y, Targets(target_id).y], ...
             'b-', 'LineWidth', 2, 'Color', [0 0 1 0.6]);
    end
end

% 绘制无人机节点
for i = 1:length(UAVs)
    plot(UAVs(i).x, UAVs(i).y, '^', 'MarkerSize', 8, ...
         'MarkerFaceColor', 'blue', 'MarkerEdgeColor', 'black');
end

% 绘制目标节点
for i = 1:length(Targets)
    plot(Targets(i).x, Targets(i).y, 'o', 'MarkerSize', 8, ...
         'MarkerFaceColor', 'red', 'MarkerEdgeColor', 'black');
end

title('任务分配网络图');
xlabel('X坐标 (m)');
ylabel('Y坐标 (m)');
legend('分配连接', '无人机', '目标', 'Location', 'best');
grid on;
hold off;

end

function createThreatHeatmap(Targets)
% 创建威胁分布热力图

figure('Position', [400, 400, 800, 600]);

% 创建网格
battlefield_size = 10000;
grid_size = 50;
x_grid = 0:grid_size:battlefield_size;
y_grid = 0:grid_size:battlefield_size;
[X, Y] = meshgrid(x_grid, y_grid);

% 计算威胁密度
threat_density = zeros(size(X));
for i = 1:length(Targets)
    % 使用高斯核函数计算威胁影响
    sigma = 1000;  % 影响范围
    threat_influence = Targets(i).threat * exp(-((X - Targets(i).x).^2 + (Y - Targets(i).y).^2) / (2 * sigma^2));
    threat_density = threat_density + threat_influence;
end

% 绘制热力图
contourf(X, Y, threat_density, 20, 'LineStyle', 'none');
colorbar;
colormap('hot');

hold on;
% 叠加目标位置
for i = 1:length(Targets)
    plot(Targets(i).x, Targets(i).y, 'wo', 'MarkerSize', 8, 'MarkerFaceColor', 'white');
end

title('威胁分布热力图');
xlabel('X坐标 (m)');
ylabel('Y坐标 (m)');
hold off;

end

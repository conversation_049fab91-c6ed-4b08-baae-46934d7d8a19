%% 任务分配可视化 - 显示无人机到目标的具体分配
% 这才是客户真正想要的：每个无人机分配给哪个具体目标

clear;
clc;
close all;

fprintf('=== 生成任务分配可视化图 ===\n');

%% 运行完整算法获取分配结果
[UAVs, Targets, target_clusters, UAV_teams, all_assignments, interception_points] = runCompleteAlgorithm();

%% 创建任务分配可视化图
createTaskAssignmentVisualization(UAVs, Targets, target_clusters, UAV_teams, all_assignments, interception_points);

fprintf('✓ 任务分配可视化图已生成并保存\n');

function [UAVs, Targets, target_clusters, UAV_teams, all_assignments, interception_points] = runCompleteAlgorithm()
% 运行完整的任务分配算法

% 基本参数设置
N_U = 54;
N_T = 30;
battlefield_size = 10000;
v_u = 50;
v_t = 70;
L_0 = 2000;
alpha = 1000;
b_l = 3;
b_u = 12;
lambda = 0.7;
lambda1 = 0.7;
lambda2 = 0.3;
lambda3 = 0.6;
lambda4 = 0.4;
d_min = 3000;
d_max = 8000;

rng(42);  % 固定种子确保可重现

%% 初始化UAV（三基地部署）
UAVs = struct('x', {}, 'y', {}, 'v', {}, 'theta', {});

base1_x = 2000; base1_y = 2000;
base2_x = 2000; base2_y = 6000;
base3_x = 5000; base3_y = 2000;

uavs_per_base = floor(N_U / 3);
remaining_uavs = N_U - 3 * uavs_per_base;
uav_idx = 1;

% 基地1部署
for i = 1:uavs_per_base
    angle = (i-1) * 2*pi / uavs_per_base;
    radius = 80 + 40 * (i-1) / uavs_per_base;
    UAVs(uav_idx).x = base1_x + radius * cos(angle);
    UAVs(uav_idx).y = base1_y + radius * sin(angle);
    UAVs(uav_idx).v = v_u;
    UAVs(uav_idx).theta = angle + pi/4;
    uav_idx = uav_idx + 1;
end

% 基地2部署
for i = 1:uavs_per_base
    angle = (i-1) * 2*pi / uavs_per_base;
    radius = 80 + 40 * (i-1) / uavs_per_base;
    UAVs(uav_idx).x = base2_x + radius * cos(angle);
    UAVs(uav_idx).y = base2_y + radius * sin(angle);
    UAVs(uav_idx).v = v_u;
    UAVs(uav_idx).theta = angle - pi/4;
    uav_idx = uav_idx + 1;
end

% 基地3部署
for i = 1:(uavs_per_base + remaining_uavs)
    angle = (i-1) * 2*pi / (uavs_per_base + remaining_uavs);
    radius = 80 + 60 * (i-1) / (uavs_per_base + remaining_uavs);
    UAVs(uav_idx).x = base3_x + radius * cos(angle);
    UAVs(uav_idx).y = base3_y + radius * sin(angle);
    UAVs(uav_idx).v = v_u;
    UAVs(uav_idx).theta = angle + pi/2;
    uav_idx = uav_idx + 1;
end

%% 初始化目标
Targets = struct('x', {}, 'y', {}, 'v', {}, 'theta', {}, 'threat', {});

% 创建4个目标群组 + 2个孤立点
cluster_centers = [2500, 4000; 7500, 6000; 4500, 2500; 4500, 7000];
targets_per_cluster = [8, 7, 6, 7];
remaining_targets = N_T - sum(targets_per_cluster);

center_x = battlefield_size / 2;
center_y = battlefield_size / 2;

idx = 1;
for c = 1:4
    for i = 1:targets_per_cluster(c)
        angle = (i-1) * 2*pi / targets_per_cluster(c) + 0.3*randn();
        radius = 300 + 200*rand();
        Targets(idx).x = cluster_centers(c, 1) + radius * cos(angle);
        Targets(idx).y = cluster_centers(c, 2) + radius * sin(angle);
        Targets(idx).v = v_t * (0.8 + 0.4*rand());
        Targets(idx).theta = angle + pi/4 + 0.5*randn();
        
        d = sqrt((Targets(idx).x - center_x)^2 + (Targets(idx).y - center_y)^2);
        W1 = calculateDistanceThreat(d, d_min, d_max);
        W2 = log(1 + Targets(idx).v^2);
        Targets(idx).threat = lambda3 * W1 + lambda4 * W2;
        
        idx = idx + 1;
    end
end

% 添加孤立点
for i = 1:remaining_targets
    Targets(idx).x = rand() * battlefield_size;
    Targets(idx).y = rand() * battlefield_size;
    Targets(idx).v = v_t * (0.5 + rand());
    Targets(idx).theta = rand() * 2*pi;
    
    d = sqrt((Targets(idx).x - center_x)^2 + (Targets(idx).y - center_y)^2);
    W1 = calculateDistanceThreat(d, d_min, d_max);
    W2 = log(1 + Targets(idx).v^2);
    Targets(idx).threat = lambda3 * W1 + lambda4 * W2;
    
    idx = idx + 1;
end

%% 执行完整算法
fprintf('执行特征相似性聚类...\n');
[M_d, M_v, M_theta] = calculateSimilarityMatrices(Targets, alpha);
target_clusters = featureSimilarityClustering(M_d, M_v, M_theta, lambda, b_l, b_u, Targets);

fprintf('执行无人机分配...\n');
D = calculateInterceptionAdvantage(UAVs, target_clusters, lambda1, lambda2, Targets);
UAV_teams = assignUAVs(D, target_clusters, N_U);

fprintf('执行任务分配...\n');
num_clusters = length(target_clusters);
all_assignments = cell(1, num_clusters);
interception_points = cell(1, num_clusters);

for q = 1:num_clusters
    current_UAVs = UAVs(UAV_teams{q});
    current_targets = Targets(target_clusters{q});
    P = calculateInterceptionProbability(current_UAVs, current_targets, v_u, v_t, L_0);
    all_assignments{q} = networkFlowTaskAssignment(current_UAVs, current_targets, P);
    interception_points{q} = designInterceptionPoints(current_UAVs, current_targets, all_assignments{q}, L_0, v_u, v_t);
end

rng('shuffle');

end

function createTaskAssignmentVisualization(UAVs, Targets, target_clusters, UAV_teams, all_assignments, interception_points)
% 创建任务分配可视化图

% 创建大尺寸高分辨率图形
fig = figure('Position', [100, 100, 1600, 1200]);
set(fig, 'Color', 'white', 'PaperPositionMode', 'auto');

% 使用专业配色方案
cluster_colors = [
    0.2000, 0.6000, 0.8000;  % 蓝色
    0.8000, 0.4000, 0.2000;  % 橙色
    0.2000, 0.8000, 0.4000;  % 绿色
    0.8000, 0.2000, 0.6000;  % 紫红色
    0.6000, 0.8000, 0.2000;  % 黄绿色
];

hold on;

battlefield_size = 10000;
xlim([0, battlefield_size]);
ylim([0, battlefield_size]);

% 绘制网格
grid on;
set(gca, 'GridAlpha', 0.3, 'GridLineStyle', '-');

% 统计分配信息
total_assignments = 0;
multi_uav_targets = 0;

% 绘制每个聚类的任务分配
for q = 1:length(target_clusters)
    cluster = target_clusters{q};
    team = UAV_teams{q};
    assignments = all_assignments{q};
    i_points = interception_points{q};
    
    color_idx = mod(q-1, size(cluster_colors, 1)) + 1;
    current_color = cluster_colors(color_idx, :);
    
    % 绘制目标
    for j = 1:length(cluster)
        target_id = cluster(j);
        threat_level = Targets(target_id).threat;
        
        % 根据威胁等级调整标记
        if threat_level > 0.7
            marker_size = 14;
            marker_style = 's';
        elseif threat_level > 0.4
            marker_size = 12;
            marker_style = 'o';
        else
            marker_size = 10;
            marker_style = '^';
        end
        
        % 检查该目标是否被分配
        target_assignments = sum(assignments(:, j));
        if target_assignments > 0
            face_color = current_color;
            edge_color = 'black';
            line_width = 3;
            total_assignments = total_assignments + 1;
            if target_assignments > 1
                multi_uav_targets = multi_uav_targets + 1;
            end
        else
            face_color = 'white';
            edge_color = current_color;
            line_width = 2;
        end
        
        plot(Targets(target_id).x, Targets(target_id).y, marker_style, ...
            'MarkerSize', marker_size, 'MarkerEdgeColor', edge_color, ...
            'MarkerFaceColor', face_color, 'LineWidth', line_width);
        
        % 目标标签
        text(Targets(target_id).x + 150, Targets(target_id).y + 150, ...
            sprintf('T%d', target_id), 'FontSize', 9, 'Color', current_color, ...
            'FontWeight', 'bold', 'BackgroundColor', 'white', ...
            'EdgeColor', current_color, 'Margin', 2);
    end
    
    % 绘制无人机
    for i = 1:length(team)
        uav_id = team(i);
        
        % 检查该无人机是否有分配
        uav_assignments = sum(assignments(i, :));
        if uav_assignments > 0
            face_color = current_color;
            edge_color = 'black';
            marker_size = 10;
        else
            face_color = 'white';
            edge_color = current_color;
            marker_size = 8;
        end
        
        plot(UAVs(uav_id).x, UAVs(uav_id).y, '^', ...
            'MarkerSize', marker_size, 'MarkerEdgeColor', edge_color, ...
            'MarkerFaceColor', face_color, 'LineWidth', 2);
        
        % 无人机标签
        text(UAVs(uav_id).x + 100, UAVs(uav_id).y - 200, ...
            sprintf('U%d', uav_id), 'FontSize', 8, 'Color', 'blue', ...
            'FontWeight', 'bold');
    end
    
    % 绘制任务分配连线和拦截点
    [uav_indices, target_indices] = find(assignments);
    
    for k = 1:length(uav_indices)
        uav_local_idx = uav_indices(k);
        target_local_idx = target_indices(k);
        uav_id = team(uav_local_idx);
        target_id = cluster(target_local_idx);
        
        % 绘制分配连线（无人机到目标）
        plot([UAVs(uav_id).x, Targets(target_id).x], ...
             [UAVs(uav_id).y, Targets(target_id).y], ...
             '-', 'Color', current_color, 'LineWidth', 2, 'LineStyle', '--');
        
        % 如果有拦截点，绘制拦截点和路径
        if ~isempty(i_points)
            for p = 1:length(i_points)
                if i_points(p).uav_id == uav_id && i_points(p).target_id == target_id
                    % 绘制拦截点
                    plot(i_points(p).x, i_points(p).y, 'x', ...
                        'MarkerSize', 15, 'MarkerEdgeColor', 'red', 'LineWidth', 4);
                    
                    % 绘制无人机到拦截点的路径
                    plot([UAVs(uav_id).x, i_points(p).x], ...
                         [UAVs(uav_id).y, i_points(p).y], ...
                         '-', 'Color', 'blue', 'LineWidth', 3);
                    
                    % 绘制目标到拦截点的路径
                    plot([Targets(target_id).x, i_points(p).x], ...
                         [Targets(target_id).y, i_points(p).y], ...
                         ':', 'Color', 'red', 'LineWidth', 2);
                    
                    break;
                end
            end
        end
    end
end

% 设置图形属性
title('Task Assignment Visualization - UAV to Target Assignment', ...
      'FontSize', 18, 'FontWeight', 'bold');
xlabel('X Coordinate (m)', 'FontSize', 16);
ylabel('Y Coordinate (m)', 'FontSize', 16);
set(gca, 'FontSize', 14);

% 创建图例
legend_elements = {};
legend_labels = {};

% 添加分配状态图例
legend_elements{end+1} = plot(NaN, NaN, '^', 'MarkerSize', 10, 'MarkerFaceColor', 'blue', 'MarkerEdgeColor', 'black');
legend_labels{end+1} = 'Assigned UAV';
legend_elements{end+1} = plot(NaN, NaN, '^', 'MarkerSize', 8, 'MarkerFaceColor', 'white', 'MarkerEdgeColor', 'blue');
legend_labels{end+1} = 'Unassigned UAV';

legend_elements{end+1} = plot(NaN, NaN, 'o', 'MarkerSize', 12, 'MarkerFaceColor', 'red', 'MarkerEdgeColor', 'black');
legend_labels{end+1} = 'Assigned Target';
legend_elements{end+1} = plot(NaN, NaN, 'o', 'MarkerSize', 10, 'MarkerFaceColor', 'white', 'MarkerEdgeColor', 'red');
legend_labels{end+1} = 'Unassigned Target';

legend_elements{end+1} = plot(NaN, NaN, '--', 'Color', 'black', 'LineWidth', 2);
legend_labels{end+1} = 'Assignment Link';
legend_elements{end+1} = plot(NaN, NaN, '-', 'Color', 'blue', 'LineWidth', 3);
legend_labels{end+1} = 'UAV Path';
legend_elements{end+1} = plot(NaN, NaN, ':', 'Color', 'red', 'LineWidth', 2);
legend_labels{end+1} = 'Target Path';
legend_elements{end+1} = plot(NaN, NaN, 'x', 'MarkerSize', 15, 'MarkerEdgeColor', 'red', 'LineWidth', 4);
legend_labels{end+1} = 'Interception Point';

legend([legend_elements{:}], legend_labels, 'Location', 'northeast', ...
       'FontSize', 11, 'Box', 'on');

% 添加统计信息
assignment_rate = total_assignments / length(Targets) * 100;
multi_uav_rate = multi_uav_targets / length(Targets) * 100;

stats_text = sprintf(['Assignment Statistics:\n' ...
                     '• Total targets: %d\n' ...
                     '• Assigned targets: %d (%.1f%%)\n' ...
                     '• Multi-UAV targets: %d (%.1f%%)\n' ...
                     '• Total UAVs: %d\n' ...
                     '• Assignment efficiency: %.1f%%'], ...
                     length(Targets), total_assignments, assignment_rate, ...
                     multi_uav_targets, multi_uav_rate, length(UAVs), assignment_rate);

annotation('textbox', [0.02, 0.02, 0.28, 0.20], 'String', stats_text, ...
           'FontSize', 12, 'BackgroundColor', 'white', 'EdgeColor', 'black', ...
           'FitBoxToText', 'on', 'Margin', 5);

hold off;

% 保存高质量图形
print(fig, 'TaskAssignment_Visualization', '-dpng', '-r300');
saveas(fig, 'TaskAssignment_Visualization.fig');

fprintf('任务分配可视化图已保存为:\n');
fprintf('  - TaskAssignment_Visualization.png (高分辨率PNG)\n');
fprintf('  - TaskAssignment_Visualization.fig (MATLAB格式)\n');

end

function W1 = calculateDistanceThreat(d, d_min, d_max)
% 计算基于距离的威胁度
if d <= d_min
    W1 = 1;
elseif d >= d_max
    W1 = 0;
else
    W1 = (d_max - d) / (d_max - d_min);
end
end

%% 测试所有无人机生成模式
% 快速验证三种模式都能正常工作
clear;
clc;
close all;

% 基本参数
N_U = 15;  % 较少的无人机便于观察
battlefield_size = 10000;
v_u = 50;

fprintf('=== 测试所有无人机生成模式 ===\n\n');

% 创建图形窗口
figure('Position', [100, 100, 1500, 500]);

%% 测试模式1：随机生成
fprintf('测试模式1：随机生成\n');
UAVs_mode1 = struct('x', {}, 'y', {}, 'v', {}, 'theta', {});

for i = 1:N_U
    UAVs_mode1(i).x = rand() * battlefield_size;
    UAVs_mode1(i).y = rand() * battlefield_size;
    UAVs_mode1(i).v = v_u * (0.9 + rand() * 0.2);
    UAVs_mode1(i).theta = rand() * 2 * pi;
end

% 可视化模式1
subplot(1, 3, 1);
hold on;
for i = 1:N_U
    plot(UAVs_mode1(i).x, UAVs_mode1(i).y, 'r^', 'MarkerSize', 8, 'MarkerFaceColor', 'r');
    quiver(UAVs_mode1(i).x, UAVs_mode1(i).y, ...
        UAVs_mode1(i).v*cos(UAVs_mode1(i).theta), ...
        UAVs_mode1(i).v*sin(UAVs_mode1(i).theta), 0.3, 'r');
    text(UAVs_mode1(i).x+100, UAVs_mode1(i).y+100, num2str(i), 'FontSize', 8);
end
title('模式1：随机生成');
xlabel('X坐标 (m)');
ylabel('Y坐标 (m)');
grid on;
axis([0 battlefield_size 0 battlefield_size]);
hold off;

%% 测试模式2：固定生成（三基地）
fprintf('测试模式2：固定生成（三基地）\n');
UAVs_mode2 = struct('x', {}, 'y', {}, 'v', {}, 'theta', {});

rng(42);
% 三个基地位置
base1_x = 2000; base1_y = 2000;
base2_x = 2000; base2_y = 6000;
base3_x = 5000; base3_y = 2000;

uavs_per_base = floor(N_U / 3);
remaining_uavs = N_U - 3 * uavs_per_base;
uav_idx = 1;

% 基地1部署
for i = 1:uavs_per_base
    angle = (i-1) * 2*pi / uavs_per_base;
    radius = 80 + 40 * (i-1) / uavs_per_base;
    UAVs_mode2(uav_idx).x = base1_x + radius * cos(angle);
    UAVs_mode2(uav_idx).y = base1_y + radius * sin(angle);
    UAVs_mode2(uav_idx).v = v_u;
    UAVs_mode2(uav_idx).theta = angle + pi/4;
    uav_idx = uav_idx + 1;
end

% 基地2部署
for i = 1:uavs_per_base
    angle = (i-1) * 2*pi / uavs_per_base;
    radius = 80 + 40 * (i-1) / uavs_per_base;
    UAVs_mode2(uav_idx).x = base2_x + radius * cos(angle);
    UAVs_mode2(uav_idx).y = base2_y + radius * sin(angle);
    UAVs_mode2(uav_idx).v = v_u;
    UAVs_mode2(uav_idx).theta = angle - pi/4;
    uav_idx = uav_idx + 1;
end

% 基地3部署
for i = 1:(uavs_per_base + remaining_uavs)
    angle = (i-1) * 2*pi / (uavs_per_base + remaining_uavs);
    radius = 80 + 60 * (i-1) / (uavs_per_base + remaining_uavs);
    UAVs_mode2(uav_idx).x = base3_x + radius * cos(angle);
    UAVs_mode2(uav_idx).y = base3_y + radius * sin(angle);
    UAVs_mode2(uav_idx).v = v_u;
    UAVs_mode2(uav_idx).theta = angle + pi/2;
    uav_idx = uav_idx + 1;
end

% 可视化模式2
subplot(1, 3, 2);
hold on;
% 绘制基地
plot(base1_x, base1_y, 'ks', 'MarkerSize', 12, 'MarkerFaceColor', 'k');
plot(base2_x, base2_y, 'ks', 'MarkerSize', 12, 'MarkerFaceColor', 'k');
plot(base3_x, base3_y, 'ks', 'MarkerSize', 12, 'MarkerFaceColor', 'k');

for i = 1:N_U
    plot(UAVs_mode2(i).x, UAVs_mode2(i).y, 'g^', 'MarkerSize', 8, 'MarkerFaceColor', 'g');
    quiver(UAVs_mode2(i).x, UAVs_mode2(i).y, ...
        UAVs_mode2(i).v*cos(UAVs_mode2(i).theta), ...
        UAVs_mode2(i).v*sin(UAVs_mode2(i).theta), 0.3, 'g');
    text(UAVs_mode2(i).x+100, UAVs_mode2(i).y+100, num2str(i), 'FontSize', 8);
end
title('模式2：固定生成（三基地）');
xlabel('X坐标 (m)');
ylabel('Y坐标 (m)');
grid on;
axis([0 battlefield_size 0 battlefield_size]);
hold off;

%% 测试模式3：高度集中（螺旋编队）
fprintf('测试模式3：高度集中（螺旋编队）\n');
UAVs_mode3 = struct('x', {}, 'y', {}, 'v', {}, 'theta', {});

% 单一基地位置
center_x = battlefield_size * 0.3;
center_y = battlefield_size * 0.4;

for i = 1:N_U
    spiral_angle = i * pi/3;
    spiral_radius = 50 + i * 12;
    UAVs_mode3(i).x = center_x + spiral_radius * cos(spiral_angle);
    UAVs_mode3(i).y = center_y + spiral_radius * sin(spiral_angle);
    UAVs_mode3(i).v = v_u * (0.95 + 0.1 * sin(spiral_angle));
    UAVs_mode3(i).theta = spiral_angle + pi/2;
end

rng('shuffle');

% 可视化模式3
subplot(1, 3, 3);
hold on;
% 绘制中心基地
plot(center_x, center_y, 'ks', 'MarkerSize', 15, 'MarkerFaceColor', 'k');
text(center_x+200, center_y, '集中基地', 'FontSize', 10);

for i = 1:N_U
    plot(UAVs_mode3(i).x, UAVs_mode3(i).y, 'b^', 'MarkerSize', 8, 'MarkerFaceColor', 'b');
    quiver(UAVs_mode3(i).x, UAVs_mode3(i).y, ...
        UAVs_mode3(i).v*cos(UAVs_mode3(i).theta), ...
        UAVs_mode3(i).v*sin(UAVs_mode3(i).theta), 0.3, 'b');
    text(UAVs_mode3(i).x+100, UAVs_mode3(i).y+100, num2str(i), 'FontSize', 8);
end
title('模式3：高度集中（螺旋编队）');
xlabel('X坐标 (m)');
ylabel('Y坐标 (m)');
grid on;
axis([0 battlefield_size 0 battlefield_size]);
hold off;

%% 统计对比
fprintf('\n=== 三种模式统计对比 ===\n');

mode1_x = [UAVs_mode1.x]; mode1_y = [UAVs_mode1.y];
mode2_x = [UAVs_mode2.x]; mode2_y = [UAVs_mode2.y];
mode3_x = [UAVs_mode3.x]; mode3_y = [UAVs_mode3.y];

fprintf('模式1（随机）- 覆盖范围: %.0f×%.0f, X标准差: %.0f, Y标准差: %.0f\n', ...
    max(mode1_x)-min(mode1_x), max(mode1_y)-min(mode1_y), std(mode1_x), std(mode1_y));

fprintf('模式2（三基地）- 覆盖范围: %.0f×%.0f, X标准差: %.0f, Y标准差: %.0f\n', ...
    max(mode2_x)-min(mode2_x), max(mode2_y)-min(mode2_y), std(mode2_x), std(mode2_y));

fprintf('模式3（集中）- 覆盖范围: %.0f×%.0f, X标准差: %.0f, Y标准差: %.0f\n', ...
    max(mode3_x)-min(mode3_x), max(mode3_y)-min(mode3_y), std(mode3_x), std(mode3_y));

fprintf('\n✓ 所有三种模式测试完成！\n');
fprintf('现在可以在main.m中设置UAV_generation_mode = 1, 2, 或 3 来选择相应模式\n');

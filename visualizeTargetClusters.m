function visualizeTargetClusters(Targets, target_clusters, battlefield_size)
% 可视化目标聚类结果，确保各聚类边界清晰不重叠
% 输入:
%   Targets: 目标结构体数组
%   target_clusters: 聚类结果
%   battlefield_size: 战场大小

figure;
hold on;

% 定义一组鲜明的颜色，增加对比度
colors = [
    0.8500, 0.3250, 0.0980;  % 橙色
    0.0000, 0.4470, 0.7410;  % 蓝色
    0.4660, 0.6740, 0.1880;  % 绿色
    0.4940, 0.1840, 0.5560;  % 紫色
    0.9290, 0.6940, 0.1250;  % 黄色
    0.3010, 0.7450, 0.9330;  % 浅蓝
    0.6350, 0.0780, 0.1840;  % 深红
    0.5, 0.5, 0.5;           % 灰色
    0, 0.75, 0.75;           % 青色
    0.75, 0, 0.75;           % 品红
    0.25, 0.25, 0.75;        % 深蓝
    0.75, 0.25, 0.25;        % 深红
    0.25, 0.75, 0.25;        % 深绿
];

% 扩展颜色，如果聚类数超过颜色数
num_clusters = length(target_clusters);
if num_clusters > size(colors, 1)
    colors = [colors; rand(num_clusters - size(colors, 1), 3)];
end

% 绘制每个聚类
for i = 1:num_clusters
    cluster = target_clusters{i};
    color = colors(mod(i-1, size(colors, 1)) + 1, :);
    
    % 提取聚类中的目标坐标
    targets_x = zeros(length(cluster), 1);
    targets_y = zeros(length(cluster), 1);
    for j = 1:length(cluster)
        target_id = cluster(j);
        targets_x(j) = Targets(target_id).x;
        targets_y(j) = Targets(target_id).y;
    end
    
    % 绘制目标点
    scatter(targets_x, targets_y, 100, 'filled', 'MarkerFaceColor', color, 'MarkerEdgeColor', 'k');
    
    % 绘制聚类编号
    cluster_center_x = mean(targets_x);
    cluster_center_y = mean(targets_y);
    text(cluster_center_x, cluster_center_y, num2str(i), 'FontSize', 14, 'FontWeight', 'bold', 'HorizontalAlignment', 'center');
    
    % 为聚类绘制边界
    if length(cluster) >= 3
        % 使用凸包算法计算聚类边界
        k = convhull(targets_x, targets_y);
        
        % 绘制凸包作为聚类边界
        plot(targets_x(k), targets_y(k), 'LineWidth', 2, 'Color', color);
        
        % 为凸包增加一个缓冲区，确保边界不会直接穿过点
        buffer = 300; % 边界缓冲距离
        centroid_x = mean(targets_x);
        centroid_y = mean(targets_y);
        
        % 向外扩展凸包边界
        expanded_x = targets_x(k);
        expanded_y = targets_y(k);
        for p = 1:length(k)
            % 计算从中心到顶点的向量
            dx = expanded_x(p) - centroid_x;
            dy = expanded_y(p) - centroid_y;
            
            % 归一化
            dist = sqrt(dx^2 + dy^2);
            if dist > 0
                dx = dx / dist;
                dy = dy / dist;
            else
                dx = 1;
                dy = 0;
            end
            
            % 向外扩展
            expanded_x(p) = expanded_x(p) + buffer * dx;
            expanded_y(p) = expanded_y(p) + buffer * dy;
        end
        
        % 绘制扩展的凸包边界
        plot(expanded_x, expanded_y, '--', 'LineWidth', 1.5, 'Color', color);
    elseif length(cluster) == 2
        % 对于只有两个点的聚类，绘制连接线并扩展
        line_x = [targets_x(1), targets_x(2)];
        line_y = [targets_y(1), targets_y(2)];
        
        % 计算线段的方向向量
        dx = targets_x(2) - targets_x(1);
        dy = targets_y(2) - targets_y(1);
        
        % 归一化
        dist = sqrt(dx^2 + dy^2);
        if dist > 0
            dx = dx / dist;
            dy = dy / dist;
        end
        
        % 计算垂直方向
        perpendicular_dx = -dy;
        perpendicular_dy = dx;
        
        % 绘制扩展的边界（形成一个长方形）
        buffer = 300;
        boundary_x = [
            targets_x(1) + buffer * perpendicular_dx,
            targets_x(2) + buffer * perpendicular_dx,
            targets_x(2) - buffer * perpendicular_dx,
            targets_x(1) - buffer * perpendicular_dx,
            targets_x(1) + buffer * perpendicular_dx
        ];
        
        boundary_y = [
            targets_y(1) + buffer * perpendicular_dy,
            targets_y(2) + buffer * perpendicular_dy,
            targets_y(2) - buffer * perpendicular_dy,
            targets_y(1) - buffer * perpendicular_dy,
            targets_y(1) + buffer * perpendicular_dy
        ];
        
        plot(boundary_x, boundary_y, '--', 'LineWidth', 1.5, 'Color', color);
        plot(line_x, line_y, 'LineWidth', 2, 'Color', color);
    else
        % 对于单个点的聚类，绘制圆形边界
        radius = 400;  % 半径
        theta = linspace(0, 2*pi, 100);
        circle_x = targets_x + radius * cos(theta);
        circle_y = targets_y + radius * sin(theta);
        plot(circle_x, circle_y, '--', 'LineWidth', 1.5, 'Color', color);
    end
end

% 设置坐标轴范围和标签
axis([0, battlefield_size, 0, battlefield_size]);
title('目标聚类结果', 'FontSize', 16);
xlabel('X坐标 (m)', 'FontSize', 14);
ylabel('Y坐标 (m)', 'FontSize', 14);
grid on;
hold off;

% 显示聚类数量
disp(['聚类数量: ', num2str(num_clusters)]);
for i = 1:num_clusters
    disp(['聚类 ', num2str(i), ' 大小: ', num2str(length(target_clusters{i}))]);
end

end 
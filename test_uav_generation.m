%% 测试无人机生成模式
% 此脚本用于测试和比较两种无人机生成模式
clear;
clc;
close all;

% 基本参数
N_U = 20;  % 测试用较少的无人机数量
battlefield_size = 10000;
v_u = 50;

fprintf('=== 无人机生成模式测试 ===\n\n');

%% 测试模式1：随机生成
fprintf('测试模式1：随机生成\n');
UAVs_random = struct('x', {}, 'y', {}, 'v', {}, 'theta', {});

for i = 1:N_U
    UAVs_random(i).x = rand() * battlefield_size;
    UAVs_random(i).y = rand() * battlefield_size;
    UAVs_random(i).v = v_u * (0.9 + rand() * 0.2);
    UAVs_random(i).theta = rand() * 2 * pi;
end

fprintf('随机模式 - 前5架无人机位置：\n');
for i = 1:min(5, N_U)
    fprintf('  UAV%d: (%.1f, %.1f), v=%.1f, θ=%.2f\n', ...
        i, UAVs_random(i).x, UAVs_random(i).y, UAVs_random(i).v, UAVs_random(i).theta);
end

%% 测试模式2：固定生成
fprintf('\n测试模式2：固定生成\n');
UAVs_fixed = struct('x', {}, 'y', {}, 'v', {}, 'theta', {});

% 设置固定种子
rng(42);

% 定义三个基地（更集中的位置）
base1_x = 2000;  base1_y = 2000;  % 左下基地
base2_x = 2000;  base2_y = 6000;  % 左上基地
base3_x = 5000;  base3_y = 2000;  % 右下基地

% 计算每个基地的无人机数量
uavs_per_base = floor(N_U / 3);
remaining_uavs = N_U - 3 * uavs_per_base;

uav_idx = 1;

% 基地1部署（更紧密的圆形编队）
for i = 1:uavs_per_base
    angle = (i-1) * 2*pi / uavs_per_base;
    radius = 80 + 40 * (i-1) / uavs_per_base;  % 缩小半径范围，更集中
    UAVs_fixed(uav_idx).x = base1_x + radius * cos(angle);
    UAVs_fixed(uav_idx).y = base1_y + radius * sin(angle);
    UAVs_fixed(uav_idx).v = v_u * (0.95 + 0.1 * sin(angle));
    UAVs_fixed(uav_idx).theta = angle + pi/4;  % 统一朝向右上方
    uav_idx = uav_idx + 1;
end

% 基地2部署（更紧密的圆形编队）
for i = 1:uavs_per_base
    angle = (i-1) * 2*pi / uavs_per_base;
    radius = 80 + 40 * (i-1) / uavs_per_base;  % 缩小半径范围
    UAVs_fixed(uav_idx).x = base2_x + radius * cos(angle);
    UAVs_fixed(uav_idx).y = base2_y + radius * sin(angle);
    UAVs_fixed(uav_idx).v = v_u * (0.95 + 0.1 * cos(angle));
    UAVs_fixed(uav_idx).theta = angle - pi/4;  % 统一朝向右下方
    uav_idx = uav_idx + 1;
end

% 基地3部署（更紧密的圆形编队）
for i = 1:(uavs_per_base + remaining_uavs)
    angle = (i-1) * 2*pi / (uavs_per_base + remaining_uavs);
    radius = 80 + 60 * (i-1) / (uavs_per_base + remaining_uavs);  % 稍大一些容纳更多无人机
    UAVs_fixed(uav_idx).x = base3_x + radius * cos(angle);
    UAVs_fixed(uav_idx).y = base3_y + radius * sin(angle);
    UAVs_fixed(uav_idx).v = v_u * (0.9 + 0.2 * abs(sin(angle)));
    UAVs_fixed(uav_idx).theta = angle + pi/2;  % 统一朝向上方
    uav_idx = uav_idx + 1;
end

rng('shuffle');

fprintf('固定模式 - 前5架无人机位置：\n');
for i = 1:min(5, N_U)
    fprintf('  UAV%d: (%.1f, %.1f), v=%.1f, θ=%.2f\n', ...
        i, UAVs_fixed(i).x, UAVs_fixed(i).y, UAVs_fixed(i).v, UAVs_fixed(i).theta);
end

%% 可视化对比
figure('Position', [100, 100, 1200, 500]);

% 随机模式可视化
subplot(1, 2, 1);
hold on;
for i = 1:N_U
    plot(UAVs_random(i).x, UAVs_random(i).y, 'b^', 'MarkerSize', 8, 'MarkerFaceColor', 'b');
    % 绘制速度向量
    quiver(UAVs_random(i).x, UAVs_random(i).y, ...
        UAVs_random(i).v*cos(UAVs_random(i).theta), ...
        UAVs_random(i).v*sin(UAVs_random(i).theta), 0.3, 'b');
end
title('模式1：随机生成');
xlabel('X坐标 (m)');
ylabel('Y坐标 (m)');
grid on;
axis([0 battlefield_size 0 battlefield_size]);
hold off;

% 固定模式可视化
subplot(1, 2, 2);
hold on;
% 绘制基地位置
plot(base1_x, base1_y, 'rs', 'MarkerSize', 12, 'MarkerFaceColor', 'r');
plot(base2_x, base2_y, 'rs', 'MarkerSize', 12, 'MarkerFaceColor', 'r');
plot(base3_x, base3_y, 'rs', 'MarkerSize', 12, 'MarkerFaceColor', 'r');
text(base1_x+200, base1_y, '基地1', 'FontSize', 10);
text(base2_x+200, base2_y, '基地2', 'FontSize', 10);
text(base3_x+200, base3_y, '基地3', 'FontSize', 10);

for i = 1:N_U
    plot(UAVs_fixed(i).x, UAVs_fixed(i).y, 'g^', 'MarkerSize', 8, 'MarkerFaceColor', 'g');
    % 绘制速度向量
    quiver(UAVs_fixed(i).x, UAVs_fixed(i).y, ...
        UAVs_fixed(i).v*cos(UAVs_fixed(i).theta), ...
        UAVs_fixed(i).v*sin(UAVs_fixed(i).theta), 0.3, 'g');
end
title('模式2：固定生成（基于基地部署）');
xlabel('X坐标 (m)');
ylabel('Y坐标 (m)');
grid on;
axis([0 battlefield_size 0 battlefield_size]);
legend('基地', 'Location', 'best');
hold off;

%% 统计分析
fprintf('\n=== 统计分析 ===\n');
fprintf('随机模式统计：\n');
random_x = [UAVs_random.x];
random_y = [UAVs_random.y];
fprintf('  X坐标范围: %.1f - %.1f (均值: %.1f)\n', min(random_x), max(random_x), mean(random_x));
fprintf('  Y坐标范围: %.1f - %.1f (均值: %.1f)\n', min(random_y), max(random_y), mean(random_y));

fprintf('\n固定模式统计：\n');
fixed_x = [UAVs_fixed.x];
fixed_y = [UAVs_fixed.y];
fprintf('  X坐标范围: %.1f - %.1f (均值: %.1f)\n', min(fixed_x), max(fixed_x), mean(fixed_x));
fprintf('  Y坐标范围: %.1f - %.1f (均值: %.1f)\n', min(fixed_y), max(fixed_y), mean(fixed_y));

fprintf('\n测试完成！\n');
fprintf('使用说明：\n');
fprintf('- 在main.m中修改UAV_generation_mode的值来选择生成模式\n');
fprintf('- 模式1适合测试算法的鲁棒性\n');
fprintf('- 模式2适合调试和结果重现\n');

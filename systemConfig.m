function config = systemConfig()
% 系统配置文件 - 集中管理所有参数设置
% 输出: config - 包含所有系统参数的结构体

%% 基本场景参数
config.scenario = struct();
config.scenario.N_U = 54;                    % 无人机数量
config.scenario.N_T = 30;                    % 目标数量
config.scenario.battlefield_size = 10000;    % 战场大小(m)
config.scenario.v_u = 50;                    % UAV最大速度(m/s)
config.scenario.v_t = 70;                    % 目标最大速度(m/s)
config.scenario.L_0 = 2000;                  % 探测范围(m)
config.scenario.min_radius = 20;             % 最小转弯半径(m)

%% 聚类参数
config.clustering = struct();
config.clustering.alpha = 1000;              % 允许的欧几里德距离阈值
config.clustering.b_l = 3;                   % 聚类下界
config.clustering.b_u = 12;                  % 聚类上界
config.clustering.lambda = 0.1;              % 相似度阈值
config.clustering.alpha1 = 5;                % 速度差异下限(m/s)
config.clustering.alpha2 = 10;               % 速度差异上限(m/s)
config.clustering.theta1 = 5;                % 航向角差异下限(度)
config.clustering.theta2 = 10;               % 航向角差异上限(度)

%% 权重参数
config.weights = struct();
config.weights.lambda1 = 0.7;                % 速度优势权重
config.weights.lambda2 = 0.3;                % 距离优势权重
config.weights.lambda3 = 0.6;                % 目标距离威胁权重
config.weights.lambda4 = 0.4;                % 目标机动性威胁权重

%% 威胁评估参数
config.threat = struct();
config.threat.d_min = 3000;                  % 最小允许距离(m)
config.threat.d_max = 8000;                  % 最大防御距离(m)
config.threat.high_threat_ratio = 0.3;       % 高威胁目标比例

%% 任务分配参数
config.assignment = struct();
config.assignment.multi_uav_target_ratio = 0.8;  % 多无人机拦截目标比例
config.assignment.max_uav_per_target = 2;        % 每个目标最大无人机数量
config.assignment.min_uav_per_target = 1;        % 每个目标最小无人机数量

%% 无人机生成模式参数
config.uav_generation = struct();
config.uav_generation.mode = 2;              % 1=随机, 2=三基地, 3=集中

% 三基地模式参数
config.uav_generation.base1 = [2000, 2000];  % 基地1位置
config.uav_generation.base2 = [2000, 6000];  % 基地2位置
config.uav_generation.base3 = [5000, 2000];  % 基地3位置
config.uav_generation.base_radius_min = 80;   % 基地最小半径
config.uav_generation.base_radius_max = 120;  % 基地最大半径

% 集中模式参数
config.uav_generation.center_ratio = [0.3, 0.4];  % 集中中心位置比例
config.uav_generation.spiral_radius_min = 50;      % 螺旋最小半径
config.uav_generation.spiral_radius_step = 12;     % 螺旋半径步长

%% 目标生成参数
config.target_generation = struct();
config.target_generation.num_clusters = 4;    % 目标聚类数量
config.target_generation.cluster_radius = [500, 600, 700, 400];  % 各聚类半径
config.target_generation.cluster_centers = [  % 聚类中心位置
    2500, 4000;   % 集群1中心
    7500, 6000;   % 集群2中心
    4500, 2500;   % 集群3中心
    4500, 7000    % 集群4中心
];

%% 可视化参数
config.visualization = struct();
config.visualization.enable_enhanced = true;   % 启用增强可视化
config.visualization.save_figures = false;     % 保存图形文件
config.visualization.figure_format = 'png';    % 图形格式
config.visualization.figure_dpi = 300;         % 图形分辨率

%% 性能分析参数
config.performance = struct();
config.performance.enable_detailed_analysis = true;  % 启用详细分析
config.performance.benchmark_mode = false;           % 基准测试模式
config.performance.save_results = true;              % 保存分析结果

%% 算法参数
config.algorithm = struct();
config.algorithm.max_iterations = 1000;       % 最大迭代次数
config.algorithm.convergence_threshold = 1e-6; % 收敛阈值
config.algorithm.random_seed = 42;            % 随机种子（用于可重现结果）

%% 输出参数
config.output = struct();
config.output.verbose = true;                 % 详细输出
config.output.save_workspace = false;         % 保存工作空间
config.output.log_file = '';                  % 日志文件名（空表示不记录）

%% 验证参数有效性
config = validateConfig(config);

end

function config = validateConfig(config)
% 验证配置参数的有效性

fprintf('验证系统配置参数...\n');

% 验证基本场景参数
if config.scenario.N_U <= 0 || config.scenario.N_T <= 0
    error('无人机和目标数量必须大于0');
end

if config.scenario.N_U < config.scenario.N_T
    warning('无人机数量少于目标数量，可能影响分配效果');
end

if config.scenario.v_u <= 0 || config.scenario.v_t <= 0
    error('速度参数必须大于0');
end

% 验证聚类参数
if config.clustering.b_l >= config.clustering.b_u
    error('聚类下界必须小于上界');
end

if config.clustering.lambda <= 0 || config.clustering.lambda >= 1
    error('相似度阈值必须在(0,1)范围内');
end

% 验证权重参数
weight_fields = fieldnames(config.weights);
for i = 1:length(weight_fields)
    weight_value = config.weights.(weight_fields{i});
    if weight_value < 0 || weight_value > 1
        error('权重参数%s必须在[0,1]范围内', weight_fields{i});
    end
end

% 验证权重和
if abs(config.weights.lambda1 + config.weights.lambda2 - 1) > 1e-6
    warning('lambda1和lambda2的和不等于1，将自动归一化');
    total = config.weights.lambda1 + config.weights.lambda2;
    config.weights.lambda1 = config.weights.lambda1 / total;
    config.weights.lambda2 = config.weights.lambda2 / total;
end

if abs(config.weights.lambda3 + config.weights.lambda4 - 1) > 1e-6
    warning('lambda3和lambda4的和不等于1，将自动归一化');
    total = config.weights.lambda3 + config.weights.lambda4;
    config.weights.lambda3 = config.weights.lambda3 / total;
    config.weights.lambda4 = config.weights.lambda4 / total;
end

% 验证威胁评估参数
if config.threat.d_min >= config.threat.d_max
    error('最小威胁距离必须小于最大威胁距离');
end

% 验证任务分配参数
if config.assignment.multi_uav_target_ratio < 0 || config.assignment.multi_uav_target_ratio > 1
    error('多无人机拦截目标比例必须在[0,1]范围内');
end

if config.assignment.min_uav_per_target > config.assignment.max_uav_per_target
    error('每目标最小无人机数不能大于最大无人机数');
end

% 验证无人机生成模式
if ~ismember(config.uav_generation.mode, [1, 2, 3])
    error('无人机生成模式必须是1、2或3');
end

% 验证目标生成参数
if length(config.target_generation.cluster_radius) ~= config.target_generation.num_clusters
    error('聚类半径数量必须等于聚类数量');
end

if size(config.target_generation.cluster_centers, 1) ~= config.target_generation.num_clusters
    error('聚类中心数量必须等于聚类数量');
end

fprintf('✓ 配置参数验证通过\n');

end

function printConfig(config)
% 打印配置信息

fprintf('\n=== 系统配置信息 ===\n');

fprintf('\n场景参数:\n');
fprintf('  无人机数量: %d\n', config.scenario.N_U);
fprintf('  目标数量: %d\n', config.scenario.N_T);
fprintf('  战场大小: %d m\n', config.scenario.battlefield_size);
fprintf('  无人机速度: %d m/s\n', config.scenario.v_u);
fprintf('  目标速度: %d m/s\n', config.scenario.v_t);

fprintf('\n聚类参数:\n');
fprintf('  距离阈值: %d m\n', config.clustering.alpha);
fprintf('  聚类下界: %d\n', config.clustering.b_l);
fprintf('  聚类上界: %d\n', config.clustering.b_u);
fprintf('  相似度阈值: %.2f\n', config.clustering.lambda);

fprintf('\n权重参数:\n');
fprintf('  速度优势权重: %.2f\n', config.weights.lambda1);
fprintf('  距离优势权重: %.2f\n', config.weights.lambda2);
fprintf('  距离威胁权重: %.2f\n', config.weights.lambda3);
fprintf('  机动威胁权重: %.2f\n', config.weights.lambda4);

fprintf('\n无人机生成模式: %d\n', config.uav_generation.mode);
mode_names = {'随机生成', '三基地部署', '高度集中'};
fprintf('  模式名称: %s\n', mode_names{config.uav_generation.mode});

end

% 如果直接运行此文件，显示配置信息
if nargout == 0
    config = systemConfig();
    printConfig(config);
end

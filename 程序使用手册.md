# 多无人机任务分配系统 - 用户使用手册

## 📖 程序简介

本程序是基于论文《Hierarchical Task Assignment for Multi-UAV System in Large-Scale Group-to-Group Interception Scenarios》的完整实现，用于解决大规模多无人机系统的任务分配问题。

### 🎯 程序功能
- **智能聚类**：将大量目标自动分组，降低计算复杂度
- **优化分配**：为每个无人机分配最合适的拦截目标
- **可视化展示**：生成直观的任务分配图表
- **性能分析**：提供详细的分配统计信息

### 🔬 应用场景
- 军事防御：多无人机拦截入侵目标
- 搜救任务：多机协同搜索救援
- 监控巡逻：区域监控任务分配
- 科研仿真：算法性能验证

## 💻 系统要求

### 必需软件
- **MATLAB R2018b或更高版本**
- **优化工具箱** (Optimization Toolbox)
- **统计和机器学习工具箱** (Statistics and Machine Learning Toolbox)

### 硬件建议
- **内存**：至少4GB RAM
- **处理器**：Intel i5或同等性能
- **存储**：至少100MB可用空间

## 📁 文件结构说明

### 🚀 核心程序文件
```
main.m                              # 主程序入口（从这里开始运行）
├── systemConfig.m                  # 系统配置参数
├── featureSimilarityClustering.m   # 目标聚类算法
├── assignUAVs.m                    # 无人机团队分配
├── networkFlowTaskAssignment.m    # 任务分配优化
└── taskAssignmentVisualization.m  # 结果可视化
```

### 🔧 辅助功能文件
```
calculateSimilarityMatrices.m       # 计算目标相似性
calculateInterceptionAdvantage.m    # 计算拦截优势
calculateInterceptionProbability.m  # 计算拦截概率
calculateDistanceThreat.m          # 计算威胁等级
designInterceptionPoints.m         # 设计拦截点
displayStatistics.m               # 显示统计信息
```

### 📚 文档资料
```
程序使用手册.md                    # 本文档（使用指南）
任务分配算法详解.md                # 算法技术文档
README.md                         # 项目说明
论文.txt                          # 原始论文内容
Hierarchical Task Assignment...pdf # 原始论文PDF
```

### 📊 输出结果
```
TaskAssignment_Visualization.png   # 任务分配可视化图片
TaskAssignment_Visualization.fig   # MATLAB图形文件
```

## 🚀 快速开始

### 第一步：打开MATLAB
1. 启动MATLAB软件
2. 将工作目录切换到程序所在文件夹
3. 确保所有.m文件都在当前目录中

### 第二步：运行程序
```matlab
% 在MATLAB命令窗口中输入：
main
```
或者：
1. 双击打开 `main.m` 文件
2. 点击MATLAB工具栏中的"运行"按钮（绿色三角形）

### 第三步：查看结果
程序运行完成后会：
1. 在命令窗口显示详细的分配统计
2. 自动弹出可视化图表窗口
3. 保存结果图片到当前目录

## ⚙️ 参数配置

### 🎛️ 基本参数（在main.m文件中修改）

#### 规模参数
```matlab
N_U = 54;                    % 无人机数量
N_T = 30;                    % 目标数量
battlefield_size = 10000;    % 战场大小(米)
```

#### 性能参数
```matlab
v_u = 50;                    % 无人机最大速度(m/s)
v_t = 70;                    % 目标最大速度(m/s)
L_0 = 2000;                  % 探测范围(米)
```

#### 聚类参数
```matlab
b_l = 3;                     % 最小聚类大小
b_u = 12;                    % 最大聚类大小
lambda = 0.7;                % 相似度阈值
```

### 🎯 无人机生成模式
```matlab
UAV_generation_mode = 1;     % 修改这个值选择生成方式
```
- **模式1**：随机生成（推荐用于性能测试）
- **模式2**：三基地部署（结果可重现）
- **模式3**：高度集中（测试密集协同）

## 📊 结果解读

### 📈 统计信息说明
程序运行后会显示以下统计：

```
======= 聚类统计 =======
聚类数量: 5
各聚类大小: [12, 6, 4, 5, 3]
平均聚类大小: 6.0

======= 任务分配统计 =======
单个无人机拦截的目标数量: 5
多个无人机拦截的目标数量: 25
未分配无人机的目标数量: 0
目标分配率: 100%
多无人机拦截率: 83.3%
```

### 📊 可视化图表说明
生成的图表包含：
- **蓝色圆圈**：无人机位置
- **红色三角形**：目标位置
- **绿色连线**：任务分配关系
- **黄色圆圈**：拦截点位置
- **不同颜色区域**：目标聚类

### 🎯 性能指标
- **目标分配率**：应该达到100%
- **多无人机拦截率**：目标80%以上
- **计算时间**：通常在几秒内完成

## ❓ 常见问题

### Q1: 程序运行出错怎么办？
**A1**: 检查以下几点：
1. 确保MATLAB版本符合要求
2. 检查是否安装了优化工具箱
3. 确保所有.m文件都在同一目录
4. 重启MATLAB后重试

### Q2: 结果每次都不一样？
**A2**: 这是正常的，因为：
1. 随机生成模式下，初始位置每次不同
2. 如需固定结果，设置 `UAV_generation_mode = 2`

### Q3: 程序运行很慢？
**A3**: 可能原因：
1. 无人机或目标数量过多
2. 电脑性能不足
3. 尝试减少N_U和N_T的值

### Q4: 多无人机拦截率达不到80%？
**A4**: 调整以下参数：
1. 增加无人机数量N_U
2. 调整聚类参数lambda
3. 检查威胁度权重设置

## 🔧 进阶使用

### 自定义场景
1. 修改main.m中的参数
2. 调整无人机和目标的初始分布
3. 修改威胁度计算方式

### 算法优化
1. 调整相似性计算参数
2. 修改拦截优势权重
3. 优化网络流约束条件

### 结果分析
1. 使用displayStatistics.m查看详细统计
2. 分析不同参数对性能的影响
3. 对比不同生成模式的效果

## 📞 技术支持

如果遇到问题，请：
1. 查看MATLAB命令窗口的错误信息
2. 检查参数设置是否合理
3. 参考《任务分配算法详解.md》了解算法细节
4. 确保系统满足运行要求

---

**祝您使用愉快！** 🎉

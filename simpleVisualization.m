function simpleVisualization(UAVs, Targets, target_clusters, UAV_teams, all_assignments, interception_points, performance_results)
% 简化可视化模块 - 兼容性更好的可视化功能
% 输入:
%   UAVs: 无人机结构体数组
%   Targets: 目标结构体数组
%   target_clusters: 目标聚类结果
%   UAV_teams: 无人机团队分配结果
%   all_assignments: 任务分配结果
%   interception_points: 拦截点
%   performance_results: 性能分析结果

fprintf('=== 生成简化可视化图表 ===\n');

%% 1. 综合态势图
createSimpleSituationMap(UAVs, Targets, target_clusters, UAV_teams, all_assignments, interception_points);

%% 2. 性能指标图表
createSimplePerformanceCharts(performance_results);

%% 3. 聚类分析图
createSimpleClusterAnalysis(Targets, target_clusters);

fprintf('✓ 所有简化可视化图表已生成\n');

end

function createSimpleSituationMap(UAVs, Targets, target_clusters, UAV_teams, all_assignments, interception_points)
% 创建简化的综合态势图

figure('Position', [50, 50, 1200, 800]);

% 设置颜色方案
colors = lines(length(target_clusters));
battlefield_size = 10000;

hold on;

% 绘制战场边界
plot([0 battlefield_size battlefield_size 0 0], [0 0 battlefield_size battlefield_size 0], 'k-', 'LineWidth', 2);

% 绘制目标和聚类
for i = 1:length(target_clusters)
    cluster = target_clusters{i};
    
    % 绘制聚类边界（简化版）
    if length(cluster) > 2
        cluster_x = [Targets(cluster).x];
        cluster_y = [Targets(cluster).y];
        try
            k = convhull(cluster_x, cluster_y);
            plot(cluster_x(k), cluster_y(k), '--', 'Color', colors(i,:), 'LineWidth', 2);
        catch
            % 如果convhull失败，跳过边界绘制
        end
    end
    
    % 绘制目标
    for j = 1:length(cluster)
        target_id = cluster(j);
        threat_size = 6 + Targets(target_id).threat * 8;  % 威胁度影响大小
        
        plot(Targets(target_id).x, Targets(target_id).y, 'o', ...
            'MarkerSize', threat_size, 'MarkerEdgeColor', colors(i,:), ...
            'MarkerFaceColor', colors(i,:), 'LineWidth', 2);
        
        % 绘制速度向量
        quiver(Targets(target_id).x, Targets(target_id).y, ...
            Targets(target_id).v*cos(Targets(target_id).theta), ...
            Targets(target_id).v*sin(Targets(target_id).theta), ...
            0.5, 'Color', colors(i,:), 'LineWidth', 1.5);
        
        % 添加目标标签
        text(Targets(target_id).x + 150, Targets(target_id).y + 150, ...
            sprintf('T%d', target_id), 'FontSize', 8, 'Color', colors(i,:), 'FontWeight', 'bold');
    end
end

% 绘制无人机和任务分配
for i = 1:length(target_clusters)
    team = UAV_teams{i};
    assignments = all_assignments{i};
    
    % 绘制无人机
    for j = 1:length(team)
        uav_id = team(j);
        
        % 检查该无人机是否有任务分配
        has_assignment = sum(assignments(j, :)) > 0;
        if has_assignment
            marker_style = '^';
            face_color = colors(i,:);
            edge_color = 'black';
            marker_size = 10;
        else
            marker_style = '^';
            face_color = 'white';
            edge_color = colors(i,:);
            marker_size = 8;
        end
        
        plot(UAVs(uav_id).x, UAVs(uav_id).y, marker_style, ...
            'MarkerSize', marker_size, 'MarkerEdgeColor', edge_color, ...
            'MarkerFaceColor', face_color, 'LineWidth', 2);
        
        % 绘制速度向量
        quiver(UAVs(uav_id).x, UAVs(uav_id).y, ...
            UAVs(uav_id).v*cos(UAVs(uav_id).theta), ...
            UAVs(uav_id).v*sin(UAVs(uav_id).theta), ...
            0.3, 'Color', 'blue', 'LineWidth', 1);
        
        % 添加无人机标签
        text(UAVs(uav_id).x + 100, UAVs(uav_id).y - 200, ...
            sprintf('U%d', uav_id), 'FontSize', 8, 'Color', 'blue');
    end
end

% 绘制拦截点和连线
for i = 1:length(interception_points)
    i_points = interception_points{i};
    for j = 1:length(i_points)
        % 绘制拦截点
        plot(i_points(j).x, i_points(j).y, 'x', 'MarkerSize', 12, ...
            'MarkerEdgeColor', 'red', 'LineWidth', 3);
        
        % 绘制连线
        uav_id = i_points(j).uav_id;
        target_id = i_points(j).target_id;
        
        % 无人机到拦截点
        plot([UAVs(uav_id).x, i_points(j).x], [UAVs(uav_id).y, i_points(j).y], ...
            '--', 'Color', 'blue', 'LineWidth', 2);
        
        % 目标到拦截点
        plot([Targets(target_id).x, i_points(j).x], [Targets(target_id).y, i_points(j).y], ...
            ':', 'Color', 'red', 'LineWidth', 2);
    end
end

% 设置图形属性
title('综合态势图', 'FontSize', 16, 'FontWeight', 'bold');
xlabel('X坐标 (m)', 'FontSize', 12);
ylabel('Y坐标 (m)', 'FontSize', 12);
axis([0 battlefield_size 0 battlefield_size]);
grid on;
axis equal;

% 创建简化图例
legend_handles = [];
legend_labels = {};

% 添加图例元素
h1 = plot(NaN, NaN, '^', 'MarkerSize', 10, 'MarkerFaceColor', 'blue', 'MarkerEdgeColor', 'black');
legend_handles(end+1) = h1;
legend_labels{end+1} = '已分配无人机';

h2 = plot(NaN, NaN, 'o', 'MarkerSize', 8, 'MarkerFaceColor', 'red', 'MarkerEdgeColor', 'red');
legend_handles(end+1) = h2;
legend_labels{end+1} = '目标';

h3 = plot(NaN, NaN, 'x', 'MarkerSize', 12, 'MarkerEdgeColor', 'red', 'LineWidth', 3);
legend_handles(end+1) = h3;
legend_labels{end+1} = '拦截点';

legend(legend_handles, legend_labels, 'Location', 'best');
hold off;

end

function createSimplePerformanceCharts(performance_results)
% 创建简化的性能指标图表

figure('Position', [100, 100, 1000, 600]);

% 子图1: 主要性能指标条形图
subplot(2, 2, 1);
metrics = [performance_results.assignment.assignment_rate, ...
           performance_results.assignment.multi_uav_rate, ...
           performance_results.resource.uav_utilization, ...
           performance_results.threat.high_threat_coverage] * 100;
metric_names = {'目标分配率', '多机协同率', '资源利用率', '威胁覆盖率'};

bar(metrics, 'FaceColor', [0.3 0.6 0.9]);
set(gca, 'XTickLabel', metric_names);
xtickangle(45);
ylabel('百分比 (%)');
title('主要性能指标', 'FontWeight', 'bold');
grid on;

% 在每个条形上添加数值标签
for i = 1:length(metrics)
    text(i, metrics(i) + 2, sprintf('%.1f%%', metrics(i)), ...
         'HorizontalAlignment', 'center', 'FontWeight', 'bold');
end

% 子图2: 综合得分显示
subplot(2, 2, 2);
score = performance_results.overall.score;
pie([score, 100-score], {sprintf('得分: %.1f', score), ''});
title(sprintf('综合评分: %.1f/100', score), 'FontWeight', 'bold');
colormap([0.2 0.7 0.2; 0.9 0.9 0.9]);

% 子图3: 聚类大小分布
subplot(2, 2, 3);
cluster_sizes = performance_results.clustering.cluster_sizes;
bar(cluster_sizes, 'FaceColor', [0.9 0.6 0.3]);
xlabel('聚类编号');
ylabel('聚类大小');
title('聚类大小分布', 'FontWeight', 'bold');
grid on;

% 子图4: 性能等级显示
subplot(2, 2, 4);
grade_text = performance_results.overall.grade;
text(0.5, 0.5, grade_text, 'FontSize', 16, 'FontWeight', 'bold', ...
     'HorizontalAlignment', 'center', 'VerticalAlignment', 'middle');
title('性能等级', 'FontWeight', 'bold');
axis off;
xlim([0 1]);
ylim([0 1]);

sgtitle('算法性能分析', 'FontSize', 14, 'FontWeight', 'bold');

end

function createSimpleClusterAnalysis(Targets, target_clusters)
% 创建简化的聚类分析图

figure('Position', [200, 200, 800, 600]);

% 子图1: 聚类分布
subplot(2, 2, 1);
colors = lines(length(target_clusters));
hold on;

for i = 1:length(target_clusters)
    cluster = target_clusters{i};
    cluster_x = [Targets(cluster).x];
    cluster_y = [Targets(cluster).y];
    
    scatter(cluster_x, cluster_y, 100, colors(i,:), 'filled');
    
    % 计算并绘制聚类中心
    center_x = mean(cluster_x);
    center_y = mean(cluster_y);
    plot(center_x, center_y, 'k+', 'MarkerSize', 15, 'LineWidth', 3);
    
    text(center_x + 200, center_y + 200, sprintf('C%d', i), ...
         'FontSize', 12, 'FontWeight', 'bold');
end

title('目标聚类分布');
xlabel('X坐标 (m)');
ylabel('Y坐标 (m)');
grid on;
hold off;

% 子图2: 聚类大小分布
subplot(2, 2, 2);
cluster_sizes = cellfun(@length, target_clusters);
bar(cluster_sizes, 'FaceColor', [0.3 0.6 0.9]);
xlabel('聚类编号');
ylabel('聚类大小');
title('聚类大小分布');
grid on;

% 子图3: 聚类紧密度分析
subplot(2, 2, 3);
intra_distances = zeros(1, length(target_clusters));
for i = 1:length(target_clusters)
    cluster = target_clusters{i};
    if length(cluster) > 1
        cluster_x = [Targets(cluster).x];
        cluster_y = [Targets(cluster).y];
        center_x = mean(cluster_x);
        center_y = mean(cluster_y);
        distances = sqrt((cluster_x - center_x).^2 + (cluster_y - center_y).^2);
        intra_distances(i) = mean(distances);
    end
end

bar(intra_distances, 'FaceColor', [0.9 0.6 0.3]);
xlabel('聚类编号');
ylabel('平均内部距离 (m)');
title('聚类紧密度分析');
grid on;

% 子图4: 威胁分布
subplot(2, 2, 4);
all_threats = [Targets.threat];
histogram(all_threats, 10, 'FaceColor', [0.6 0.3 0.9]);
xlabel('威胁等级');
ylabel('目标数量');
title('威胁等级分布');
grid on;

sgtitle('聚类质量分析', 'FontSize', 14, 'FontWeight', 'bold');

end

function visualizeClusteringProcess(Targets, target_clusters, battlefield_size)
% 可视化聚类过程，包括初始聚类、合并后和分割后的结果
% 输入:
%   Targets: 目标结构体数组
%   target_clusters: 聚类结果，元胞数组
%   battlefield_size: 战场大小

figure('Name', '聚类过程可视化', 'Position', [100, 100, 1200, 400]);

% 绘制所有目标的原始分布
subplot(1, 3, 1);
hold on;

% 绘制所有目标点
for i = 1:length(Targets)
    plot(Targets(i).x, Targets(i).y, 'ko', 'MarkerSize', 8, 'MarkerFaceColor', 'k');
    
    % 绘制速度向量
    quiver(Targets(i).x, Targets(i).y, ...
        Targets(i).v*cos(Targets(i).theta), ...
        Targets(i).v*sin(Targets(i).theta), 0.5, 'k');
end

title('目标原始分布');
grid on;
axis([0 battlefield_size 0 battlefield_size]);
hold off;

% 绘制聚类结果
subplot(1, 3, 2);
hold on;

% 为每个聚类指定不同的颜色
colors = hsv(length(target_clusters));

% 绘制目标及其聚类
for i = 1:length(target_clusters)
    cluster = target_clusters{i};
    
    % 绘制该聚类中的所有目标
    for j = 1:length(cluster)
        target_id = cluster(j);
        plot(Targets(target_id).x, Targets(target_id).y, 'o', 'MarkerSize', 8, ...
            'MarkerEdgeColor', colors(i,:), 'MarkerFaceColor', colors(i,:));
    end
    
    % 计算聚类中心
    if ~isempty(cluster)
        center_x = 0;
        center_y = 0;
        for j = 1:length(cluster)
            target_id = cluster(j);
            center_x = center_x + Targets(target_id).x;
            center_y = center_y + Targets(target_id).y;
        end
        center_x = center_x / length(cluster);
        center_y = center_y / length(cluster);
        
        % 绘制聚类中心和标签
        plot(center_x, center_y, 'ks', 'MarkerSize', 12, 'MarkerFaceColor', colors(i,:));
        text(center_x, center_y, ['C' num2str(i)], 'FontSize', 10, 'FontWeight', 'bold', 'Color', 'w');
    end
end

title('聚类结果');
grid on;
axis([0 battlefield_size 0 battlefield_size]);
hold off;

% 绘制带边界的聚类结果
subplot(1, 3, 3);
hold on;

% 绘制目标及其聚类边界
for i = 1:length(target_clusters)
    cluster = target_clusters{i};
    
    % 绘制该聚类中的所有目标
    for j = 1:length(cluster)
        target_id = cluster(j);
        plot(Targets(target_id).x, Targets(target_id).y, 'o', 'MarkerSize', 8, ...
            'MarkerEdgeColor', colors(i,:), 'MarkerFaceColor', colors(i,:));
    end
    
    % 计算聚类中心
    if ~isempty(cluster)
        center_x = 0;
        center_y = 0;
        for j = 1:length(cluster)
            target_id = cluster(j);
            center_x = center_x + Targets(target_id).x;
            center_y = center_y + Targets(target_id).y;
        end
        center_x = center_x / length(cluster);
        center_y = center_y / length(cluster);
        
        % 绘制聚类中心
        plot(center_x, center_y, 'ks', 'MarkerSize', 12, 'MarkerFaceColor', colors(i,:));
        
        % 绘制包围当前聚类的椭圆或圆圈
        if length(cluster) > 1
            % 计算聚类的协方差矩阵
            points = zeros(length(cluster), 2);
            for j = 1:length(cluster)
                target_id = cluster(j);
                points(j, :) = [Targets(target_id).x, Targets(target_id).y];
            end
            
            % 获取聚类的主成分
            [pc, score, ~] = pca(points);
            
            % 计算聚类的标准差
            std_dev = std(score);
            
            % 计算椭圆参数
            theta = 0:0.01:2*pi;
            
            % 确保椭圆足够大以包含所有点
            scale_factor = 1.5;  % 缩放因子，使椭圆更大一些
            ellipse_x = center_x + scale_factor * 3 * std_dev(1) * pc(1,1) * cos(theta) + scale_factor * 3 * std_dev(min(2,end)) * pc(1,2) * sin(theta);
            ellipse_y = center_y + scale_factor * 3 * std_dev(1) * pc(2,1) * cos(theta) + scale_factor * 3 * std_dev(min(2,end)) * pc(2,2) * sin(theta);
            
            % 绘制椭圆
            plot(ellipse_x, ellipse_y, '--', 'Color', colors(i,:), 'LineWidth', 2);
        else
            % 单个点的情况，绘制圆圈
            target_id = cluster(1);
            radius = 200;  % 单个点周围圆圈的半径
            theta = 0:0.01:2*pi;
            circle_x = Targets(target_id).x + radius * cos(theta);
            circle_y = Targets(target_id).y + radius * sin(theta);
            plot(circle_x, circle_y, '--', 'Color', colors(i,:), 'LineWidth', 2);
        end
        
        % 添加聚类标签
        text(center_x, center_y+50, ['Cluster ' num2str(i) ' (' num2str(length(cluster)) ')'], ...
            'FontSize', 8, 'Color', 'k', 'HorizontalAlignment', 'center');
    end
end

title('聚类边界');
grid on;
axis([0 battlefield_size 0 battlefield_size]);
hold off;

end 
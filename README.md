# 多无人机任务分配系统

> 基于论文《Hierarchical Task Assignment for Multi-UAV System in Large-Scale Group-to-Group Interception Scenarios》的完整MATLAB实现

## 🎯 项目简介

本项目实现了一个完整的多无人机任务分配系统，采用分层架构解决大规模群对群拦截场景中的任务分配问题。系统通过智能聚类、优化分配和可视化展示，为多无人机协同作战提供了高效的解决方案。

## ✨ 核心功能

- 🧠 **智能聚类**：基于特征相似性的目标自动分组
- 🎯 **优化分配**：基于拦截优势的无人机团队分配
- 🌐 **网络流优化**：最小费用最大流的任务分配
- 📊 **阿波罗尼圆模型**：精确的拦截概率计算
- 📈 **可视化系统**：直观的结果展示和分析

## 🚀 快速开始

### 系统要求
- MATLAB R2018b或更高版本
- 优化工具箱 (Optimization Toolbox)
- 统计和机器学习工具箱

### 运行步骤
```matlab
% 1. 打开MATLAB，切换到项目目录
% 2. 在命令窗口运行：
main

% 3. 查看生成的可视化结果和统计信息
```

## 📁 项目结构

```
📦 多无人机任务分配系统
├── 📋 程序使用手册.md          # 详细使用指南（小白必读）
├── 📋 任务分配算法详解.md      # 算法技术文档
├── 🚀 main.m                  # 主程序入口
├── 🔧 核心算法模块/
│   ├── featureSimilarityClustering.m
│   ├── assignUAVs.m
│   ├── networkFlowTaskAssignment.m
│   └── taskAssignmentVisualization.m
├── 🛠️ 辅助功能模块/
│   ├── calculateSimilarityMatrices.m
│   ├── calculateInterceptionAdvantage.m
│   ├── calculateInterceptionProbability.m
│   └── displayStatistics.m
└── 📚 参考资料/
    ├── 论文.txt
    └── Hierarchical Task Assignment...pdf
```

## ⚙️ 参数配置

### 基本参数
```matlab
N_U = 54;                    % 无人机数量
N_T = 30;                    % 目标数量
UAV_generation_mode = 1;     % 生成模式：1-随机，2-固定，3-集中
```

### 性能参数
```matlab
v_u = 50;                    % 无人机速度(m/s)
v_t = 70;                    % 目标速度(m/s)
battlefield_size = 10000;    % 战场大小(m)
```

## 📊 典型结果

- **目标分配率**：100%
- **多无人机拦截率**：80-100%
- **计算时间**：3-5秒（中等规模）
- **聚类效果**：自动分组为3-6个均衡聚类

## 📖 使用指南

- 🔰 **新手用户**：请先阅读《程序使用手册.md》
- 🔬 **研究人员**：参考《任务分配算法详解.md》
- 🛠️ **开发者**：查看代码注释和函数文档

## 📚 算法原理

基于论文实现的层次化任务分配框架：

1. **目标聚类** - 基于位置、速度、航向的特征相似性
2. **无人机分配** - 基于拦截优势的团队组建
3. **任务分配** - 网络流模型优化分配方案
4. **拦截点设计** - 阿波罗尼圆理论指导的最优拦截

## 🎓 学术引用

如果本项目对您的研究有帮助，请引用原始论文：
```
Wu, X.; Zhang, M.; Wang, X.; Zheng, Y.; Yu, H.
Hierarchical Task Assignment for Multi-UAV System in Large-Scale Group-to-Group Interception Scenarios.
Drones 2023, 7, 560.
```

## 📞 技术支持

遇到问题？
1. 查看《程序使用手册.md》中的常见问题
2. 检查MATLAB版本和工具箱
3. 确保所有文件在同一目录

---
**让多无人机协同更智能！** 🚁✨
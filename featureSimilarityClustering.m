function target_clusters = featureSimilarityClustering(M_d, M_v, M_theta, lambda, b_l, b_u, Targets)
% 特征相似性聚类算法，严格实现论文中的Algorithm 1
% 输入:
%   M_d: 位置相似度矩阵
%   M_v: 速度相似度矩阵
%   M_theta: 航向角相似度矩阵
%   lambda: 相似度阈值
%   b_l: 聚类下界
%   b_u: 聚类上界
%   Targets: 目标结构体数组
% 输出:
%   target_clusters: 聚类结果，元胞数组

N_T = size(M_d, 1);

% Step 1: 计算总的相似度矩阵M（论文公式7）
% 回到论文原始的乘法方式，但使用调整后的相似度矩阵
M = M_d .* M_v .* M_theta;

% Step 2: 根据相似度阈值构建邻接矩阵A
A = M >= lambda;

% 确保邻接矩阵对角线为1（自身相似度为1）
for i = 1:N_T
    A(i, i) = 1;
end

% Step 3: 寻找连通分量作为初始聚类
components = findConnectedComponents(A);

% 将连通分量转换为目标聚类
num_clusters = length(components);
clusters = cell(1, num_clusters);
for i = 1:num_clusters
    clusters{i} = components{i};
end

disp(['初始聚类数量: ', num2str(num_clusters)]);
for i = 1:num_clusters
    disp(['聚类 ', num2str(i), ' 大小: ', num2str(length(clusters{i}))]);
end

% Step 4: 迭代聚类合并直到满足约束条件
% 记录每一步的操作以便调试
disp('开始执行图合并操作...');

% 图合并操作（Algorithm 1中的第5-13行）
merged = true;
while merged
    merged = false;
    
    % 计算当前所有聚类之间的相似度
    num_clusters = length(clusters);
    cluster_similarities = zeros(num_clusters, num_clusters);
    
    for i = 1:num_clusters
        for j = i+1:num_clusters
            % 如果两个聚类都满足大小上界限制，计算它们之间的相似度
            if length(clusters{i}) <= b_u && length(clusters{j}) <= b_u
                % 计算两个聚类之间的最大相似度（论文提到的"图合并"操作）
                max_similarity = 0;
                for m = 1:length(clusters{i})
                    for n = 1:length(clusters{j})
                        node_i = clusters{i}(m);
                        node_j = clusters{j}(n);
                        if M(node_i, node_j) > max_similarity
                            max_similarity = M(node_i, node_j);
                        end
                    end
                end
                cluster_similarities(i, j) = max_similarity;
                cluster_similarities(j, i) = max_similarity;
            end
        end
    end
    
    % 寻找相似度最高的两个聚类
    [max_sim, idx] = max(cluster_similarities(:));
    if max_sim < lambda  % 如果最大相似度小于阈值，结束合并
        break;
    end
    
    [i, j] = ind2sub(size(cluster_similarities), idx);
    
    % 检查合并后的聚类大小是否超过上界
    if length(clusters{i}) + length(clusters{j}) <= b_u
        disp(['合并聚类 ', num2str(i), ' 和聚类 ', num2str(j), ...
            '，大小分别为 ', num2str(length(clusters{i})), ' 和 ', ...
            num2str(length(clusters{j})), '，相似度为 ', num2str(max_sim)]);
        
        % 合并两个聚类
        clusters{i} = [clusters{i}, clusters{j}];
        clusters(j) = [];  % 移除被合并的聚类
        merged = true;
    end
end

% Step 5: 处理不满足上界约束的聚类（图分割操作）
disp('开始执行图分割操作...');
i = 1;
while i <= length(clusters)
    % 如果聚类大小大于上界，执行分割
    if length(clusters{i}) > b_u
        disp(['聚类 ', num2str(i), ' 大小为 ', num2str(length(clusters{i})), ...
            '，大于上界 ', num2str(b_u), '，执行图分割操作']);
        
        % 提取子图的邻接矩阵
        sub_indices = clusters{i};
        sub_A = A(sub_indices, sub_indices);
        
        % 使用k-means聚类分割成k=2个子聚类
        % 创建特征矩阵，包括位置和速度特征
        features = zeros(length(sub_indices), 4);
        for k = 1:length(sub_indices)
            idx = sub_indices(k);
            features(k, 1) = Targets(idx).x;
            features(k, 2) = Targets(idx).y;
            features(k, 3) = Targets(idx).v * cos(Targets(idx).theta);  % 速度x分量
            features(k, 4) = Targets(idx).v * sin(Targets(idx).theta);  % 速度y分量
        end
        
        % 归一化特征
        features = (features - mean(features)) ./ std(features);
        
        % 使用k-means分割
        options = statset('MaxIter', 1000, 'Display', 'off');
        [idx, ~] = kmeans(features, 2, 'Replicates', 10, 'Options', options);
        
        % 创建两个新聚类
        new_cluster1 = sub_indices(idx == 1);
        new_cluster2 = sub_indices(idx == 2);
        
        % 更新聚类列表
        clusters{i} = new_cluster1;
        clusters{end+1} = new_cluster2;
        
        disp(['将聚类 ', num2str(i), ' 分割为大小分别为 ', ...
            num2str(length(new_cluster1)), ' 和 ', ...
            num2str(length(new_cluster2)), ' 的两个新聚类']);
    end
    i = i + 1;
end

% 最终检查所有聚类是否满足约束条件
final_num_clusters = length(clusters);
disp(['最终聚类数量: ', num2str(final_num_clusters)]);
for i = 1:final_num_clusters
    disp(['聚类 ', num2str(i), ' 大小: ', num2str(length(clusters{i}))]);
end

% Step 6: 处理小于下界的聚类（图合并操作）
disp('开始处理小于下界的聚类...');
merged_small = true;
while merged_small
    merged_small = false;

    for i = 1:length(clusters)
        if length(clusters{i}) < b_l
            % 找到与当前小聚类最相似的其他聚类
            best_similarity = -1;
            best_target_cluster = -1;

            for j = 1:length(clusters)
                if i ~= j && length(clusters{i}) + length(clusters{j}) <= b_u
                    % 计算两个聚类之间的平均相似度
                    total_sim = 0;
                    count = 0;
                    for k1 = 1:length(clusters{i})
                        for k2 = 1:length(clusters{j})
                            idx1 = clusters{i}(k1);
                            idx2 = clusters{j}(k2);
                            total_sim = total_sim + M(idx1, idx2);
                            count = count + 1;
                        end
                    end
                    avg_sim = total_sim / count;

                    if avg_sim > best_similarity
                        best_similarity = avg_sim;
                        best_target_cluster = j;
                    end
                end
            end

            % 如果找到合适的聚类进行合并
            if best_target_cluster > 0
                disp(['合并小聚类 ', num2str(i), ' (大小:', num2str(length(clusters{i})), ...
                    ') 到聚类 ', num2str(best_target_cluster), ' (大小:', num2str(length(clusters{best_target_cluster})), ')']);

                clusters{best_target_cluster} = [clusters{best_target_cluster}, clusters{i}];
                clusters(i) = [];
                merged_small = true;
                break;  % 重新开始循环
            end
        end
    end
end

final_num_clusters = length(clusters);
disp(['最终聚类数量: ', num2str(final_num_clusters)]);
for i = 1:final_num_clusters
    disp(['聚类 ', num2str(i), ' 大小: ', num2str(length(clusters{i}))]);
end

% 将最终结果整理为target_clusters
target_clusters = cell(1, final_num_clusters);
for i = 1:final_num_clusters
    target_clusters{i} = clusters{i};
end

end

% 使用广度优先搜索(BFS)找到图的连通分量
function components = findConnectedComponents(adjacencyMatrix)
    n = size(adjacencyMatrix, 1);
    visited = false(1, n);
    components = {};
    
    for i = 1:n
        if ~visited(i)
            % 找到一个新的连通分量
            component = [];
            queue = i;
            visited(i) = true;
            
            while ~isempty(queue)
                node = queue(1);
                queue(1) = [];
                component = [component, node];
                
                % 查找相邻节点
                neighbors = find(adjacencyMatrix(node, :));
                for j = 1:length(neighbors)
                    neighbor = neighbors(j);
                    if ~visited(neighbor)
                        queue = [queue, neighbor];
                        visited(neighbor) = true;
                    end
                end
            end
            
            components{end+1} = component;
        end
    end
end 
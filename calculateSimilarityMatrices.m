function [M_d, M_v, M_theta] = calculateSimilarityMatrices(Targets, alpha)
% 计算目标之间的相似度矩阵
% 根据论文公式(5)(6)：计算位置相似度、速度相似度和方向相似度
% 增强版：提高相似度计算的鲁棒性

N_T = length(Targets);
M_d = zeros(N_T, N_T);
M_v = zeros(N_T, N_T);
M_theta = zeros(N_T, N_T);

% 严格按照论文原始参数设置
k = 0.001;  % 位置相似度的衰减因子，论文建议值
alpha1_v = 5;   % 速度差异阈值下限，论文原值 5 m/s
alpha2_v = 10;  % 速度差异阈值上限，论文原值 10 m/s
alpha1_theta = 5 * pi/180;   % 角度差异阈值下限，论文原值 5 度
alpha2_theta = 10 * pi/180;  % 角度差异阈值上限，论文原值 10 度

% 预先计算所有目标位置，避免重复计算
positions = zeros(N_T, 2);
velocities = zeros(N_T, 2);
for i = 1:N_T
    positions(i,:) = [Targets(i).x, Targets(i).y];
    velocities(i,:) = [Targets(i).v * cos(Targets(i).theta), Targets(i).v * sin(Targets(i).theta)];
end

% 计算所有目标对之间的相似度
for i = 1:N_T
    for j = 1:N_T
        if i == j
            % 自身相似度为1
            M_d(i, j) = 1;
            M_v(i, j) = 1;
            M_theta(i, j) = 1;
        else
            % 计算位置相似度
            d_ij = sqrt(sum((positions(i,:) - positions(j,:)).^2));
            if d_ij <= alpha
                M_d(i, j) = 1;
            else
                M_d(i, j) = exp(-k * (d_ij - alpha));
            end
            
            % 计算速度相似度
            v_i = sqrt(sum(velocities(i,:).^2));
            v_j = sqrt(sum(velocities(j,:).^2));
            v_diff = abs(v_i - v_j);
            if v_diff <= alpha1_v
                M_v(i, j) = 1;
            elseif v_diff >= alpha2_v
                M_v(i, j) = 0;
            else
                M_v(i, j) = (alpha2_v - v_diff) / (alpha2_v - alpha1_v);
            end
            
            % 计算角度相似度 - 直接使用朝向角差异（更符合论文描述）
            theta_i = Targets(i).theta;
            theta_j = Targets(j).theta;

            % 计算角度差异，考虑周期性
            theta_diff = abs(theta_i - theta_j);
            theta_diff = min(theta_diff, 2*pi - theta_diff);  % 取较小的角度差

            if theta_diff <= alpha1_theta
                M_theta(i, j) = 1;
            elseif theta_diff >= alpha2_theta
                M_theta(i, j) = 0;
            else
                M_theta(i, j) = (alpha2_theta - theta_diff) / (alpha2_theta - alpha1_theta);
            end
        end
    end
end

% 调试输出
disp('相似度矩阵统计:');
disp(['  位置相似度平均值: ', num2str(mean(M_d(:)))]);
disp(['  速度相似度平均值: ', num2str(mean(M_v(:)))]);
disp(['  角度相似度平均值: ', num2str(mean(M_theta(:)))]);

end 
以下是论文的纯文本内容，已去除所有格式和图片：

```
drones

Article
Hierarchical Task Assignment for Multi-UAV System in Large-Scale Group-to-Group Interception Scenarios

<PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON><PERSON> and <PERSON><PERSON><PERSON> Yu

College of Intelligence Science and Technology, National University of Defense Technology,
Changsha 410073, China
* Correspondence: <EMAIL>

Abstract: The multi-UAV task assignment problem in large-scale group-to-group interception scenarios presents challenges in terms of large computational complexity and the lack of accurate evaluation models. This paper proposes an effective evaluation model and hierarchical task assignment framework to address these challenges. The evaluation model incorporates the dynamics constraints specific to fixed-wing UAVs and improves the Apollonius circle model to accurately describe the cooperative interception effectiveness of multiple UAVs. By evaluating the interception effectiveness during the interception process, the assignment scheme of the multiple UAVs could be given based on the model. To optimize the configuration of UAVs and targets, a hierarchical framework based on the network flow algorithm is employed. This framework utilizes a clustering method based on feature similarity and interception advantage to decompose the large-scale task assignment problem into smaller, complete submodels. Following the assignment, Dubins curves are planned to the optimal interception points, ensuring the effectiveness of the interception task. Simulation results demonstrate the feasibility and effectiveness of the proposed scheme. With the increase in the model scale, the proposed scheme has a greater descending rate of runtime. In a large-scale scenario involving 200 UAVs and 100 targets, the runtime is reduced by 84.86%.

Keywords: task assignment; multi-UAV system; large-scale assignment

Cheng Chen
<EMAIL>

Citation: Wu, X.; Zhang, M.; Wang, X.; Zheng, Y.; Yu, H. Hierarchical Task Assignment for Multi-UAV System in Large-Scale Group-to-Group Interception Scenarios. Drones 2023, 7, 560. https://doi.org/10.3390/drones7090560

Academic Editor: Abdessaltar Abdelkefi

Received: 2 August 2023
Revised: 18 August 2023
Accepted: 29 August 2023
Published: 1 September 2023

Copyright © 2023 by the authors.
Licensee MDPI, Basel, Switzerland. This article is an open access article distributed under the terms and conditions of the Creative Commons Attribution (CC BY) license (https://creativecommons.org/licenses/by/4.0/).

1. Introduction
Counter-UAV has become a hot topic, as UAV attacks are increasing and used in conflicts [1]. Traditional counter-UAV approaches include methods such as fire strikes and electromagnetic interference. With the increase in the scale of low-cost UAVs, traditional counter-UAV methods are ineffective. In the face of large-scale UAV attacks, hostile UAV swarms can be constructed to achieve counter-UAV in the form of active interception [2]. This new type of counter-UAV covers advanced technologies including intelligent decision making and control and has certain advantages. For large-scale UAV attacks, interception based on fixed-wing UAV platforms will become one of the most important means of countering [3,4]. Task assignment, as the essential component of the multi-UAV system, plays a vital role in the optimal configuration of targets and UAVs in interception scenarios [5]. Once a target is detected, the UAV needs to provide an intercept solution as soon as possible, based on the situation of both sides. In large-scale interception scenarios, it is crucial for the assignment algorithm to fulfill the prerequisites of a reliable evaluation model and efficient solution speed. Solving the task assignment problem in large-scale group-to-group interception scenarios is still a challenge.

In large-scale group-to-group interception scenarios, multiple UAVs assess the situation by considering relevant information and form an assignment plan based on the evaluation outcomes. The multi-UAV task assignment problem in large-scale interception scenarios has the following characteristics.

Drones 2023, 7, 560. https://doi.org/10.3390/drones7090560

https://www.mdpi.com/journal/drones

* *The evaluation model may need to effectively evaluate the offensive and defensive situations of both parties and integrate the task assignment result with the interception technology to ensure the successful execution of the interception task.

* *There will be clustering targets in large-scale interception scenarios, and UAVs may need to cooperate to complete the task. In addition, the execution capabilities of UAVs are different.

* *The task assignment algorithm should provide feasible solutions in a short time to accommodate the high-speed movements of fixed-wing UAVs.

The establishment of an evaluation model is the basis of task assignment. A reasonable evaluation model is essential to ensure the efficiency of group-to-group interception. Gao et al. conduct an evaluation model that takes into account the azimuth, speed, and distance advantages [6]. Traditional evaluation models typically focus on the individual value of a single UAV and seldom consider the effectiveness of multi-UAV cooperative interception. Sun et al. establish the evaluation model considering not only the relative position and velocity mentioned in traditional functions, but also relative normal velocity, normal acceleration, maneuverability, and cooperation between multiple missiles [7]. Wang et al. and Guo et al. model the cooperative interception effectiveness as a geometric coverage problem [8, 9]. The evaluation models in the literature mentioned in this article are mostly aimed at missiles or seldom consider the turning radius constraints of fixed-wing UAVs. When it comes to fixed-wing UAVs, the problem is challenging because all UAVs have different maneuvering capability and are subject to turning radius constraints [10]. Therefore, it is crucial to establish a simple but accurate evaluation model that can describe the interception scenario of the multi-UAV system.

After establishing the evaluation model, the task assignment algorithm is needed to obtain the optimal task scheme. Many scholars have proposed various methods to solve the task assignment problem. The multi-UAV task assignment problem is a non-deterministic polynomial-time hard (NP-hard) problem to find the global optimal solution [11]. The task assignment algorithm can be classified into centralized and distributed algorithms [12]. The centralized algorithm usually has more information available than the distributed algorithm so that the multi-UAV system can find the global optimal solution more efficiently [13]. In interception scenarios, the UAVs can receive all the task information detected by the radar system at the base, so the centralized algorithm has certain advantages in the initial task assignment stage. The centralized task assignment methods include optimization and heuristic algorithms [14, 15]. Heuristic algorithms such as ant colony algorithms [16], particle swarm algorithms [17], and genetic algorithms [18] rely on iterations to obtain optimal solutions. However, the efficiency of the algorithms is severely affected by the initial parameters, which are difficult to determine. Classical optimization methods include mixed-integer programming, which can clearly describe the task assignment problem [19]. In addition, graph theory-based approaches such as the network flow model use graph theory methods to formalize the characteristics of tasks and UAVs and to establish the matching relationship between tasks and UAVs to generate an effective task assignment scheme [20]. However, it is generally limited to small-scale models. With the increase in the task assignment model scale, the optimization methods face the risk of exponential computational burden [21]. It is a challenge to give solutions to large-scale task assignment problems in a short time. A significant effort in the research community is the hierarchical task assignment scheme. The idea is to decompose the complex task assignment model into several small-scale submodels. Many scholars have verified the efficiency and scalability of this scheme [22, 23, 24].

For the task assignment problem in large-scale group-to-group interception scenarios, this paper proposes a hierarchical task assignment scheme to handle the multi-UAV task assignment problem. First, this paper establishes an evaluation model to accurately describe the group-to-group interception scenario for multi-UAV systems. The evaluation model fully considers the dynamic constraints of fixed-wing UAVs and reasonably describes the cooperative interception situation. Moreover, based on a hierarchical task assignment framework, the feasible task assignment scheme will be solved under the optimality and rapidity trade-offs. Compared with the existing works, the main contributions are summarized below.

* *A simple but accurate evaluation model is designed to describe complex group-to-group cooperative interception scenarios. Based on the Apollonius circle and the fixed-wing UAV dynamics model, the evaluation model can accurately describe the cooperative interception effectiveness of multiple UAVs and guide the solution of the task assignment problem.

* *Under the hierarchical task assignment framework, this paper designs a heuristic model decomposition method for the interception scenarios. In the model decomposition phase, large-scale UAVs and targets are effectively divided based on distribution characteristics and interception advantage. In the task assignment phase, the network flow model (NFO) suitable for multi-UAV systems is established to determine the feasible solutions for each submodel. The simulation results show that the proposed algorithm can give the solution in milliseconds and reduce the runtime even more as the model scale increases.

This article is organized as follows. The scenario description and the hierarchical framework are given in Section 2. Section 3 presents the model decomposition method. Section 4 describes the evaluation model and the network flow algorithm. Section 5 provides the details of the simulation experiment. Section 6 summarizes this paper.

## 2 Problem Formulation

The typical group-to-group interception scenario in this paper is shown in Figure 1. In this scenario, multiple UAVs attempt to intercept all target clusters, and targets may adopt maneuvering strategies to evade interception after detecting multiple UAVs within the detection range \(L_{0}\). It is assumed that both UAVs and targets are fixed-wing UAV platforms, with more UAVs than targets (\(N_{U}\geq N_{T}\)). Initially, targets can be detected by a radar system. The central control station performs target and UAV grouping based on relevant information to assess the offensive and defensive situations. Then, each UAV is assigned a target suitable for interception, and excess UAVs are assigned to targets with a higher threat level for many-to-one interception to ensure higher interception effectiveness. According to the task assignment scheme, multiple UAVs will intercept multiple targets at optimal interception points.

Therefore, a hierarchical task assignment framework suitable for group-to-group interception scenarios is proposed as shown in Figure 2. First, according to the distribution of targets, feature similarity clustering is applied to decompose the targets into several subclusters. Based on the clustering results, the spatial location and requirements of each task subcluster are determined. Then, the interception advantage-based assignment method is applied to form the UAV teams for these clustered targets. After that, the large-scale task problem can be decomposed into several small-scale submodels to reduce the computational burden. Based on the decomposition results, an Apollonius circle-based evaluation function is proposed to describe the interception effectiveness of each UAV reasonably. In each submodel, the UAV broadcasts its interception effectiveness information to the central UAV. The central UAV assigns specific tasks to the UAVs within the team based on network flow optimization and plans optimal interception points to maximize the probability of successful cooperative interception. After all UAVs are assigned, the path planning algorithm based on the cooperative Dubins curve generates a path to the interception point for the UAV.

Central control station

UAV team 1

Task assignment
and path planning

Target cluster 1

Target cluster N5

Target clustering

UAV team N5

UAV grouping

Figure 2. The hierarchical task assignment framework.

**3. Model Decomposition**

This paper models the task assignment problem as a network flow model \( G = (V, E) \)
with the min-cost max-flow. In the directed graph \( G \), the number of edges in graph \( G \) is
denoted by \( m = |E| \), while the number of vertices is \( n = |V| \). Assume that the maximum
cost of each edge is \( C \), that is, \( C = max_{(i,j)\in E}c(i,j) \). The capacity of each edge is represented
by \( U = max_{(i,j)\in E}u(i,j) \). Due to the limited capacity of the network, the flow value of the
graph \( G \) will not exceed the maximum flow \( MaxFlow = mU \). Accordingly, the maximum
cost of the network is \( C \cdot MaxFlow \). The cost of the network decreases by at least one in
each iteration and reaches its minimum after several iterations. Therefore, the maximum
number of iterations of the algorithm is

\[ I \leq O(mCU). \]

Then the Bellman–Ford (BF) algorithm is used to find a path from the source point
to the sink point. The BF algorithm takes \( O(mn) \) times to find a feasible flow. Therefore,
the maximum computational complexity \( B \) of the centralized network flow algorithm to
produce a feasible task assignment solution is

\[ B = O(m^2nCU). \]

In this paper, the maximum cost \( C \) and capacity \( U \) of the network are limited to 1,
so the computational complexity in (2) can be simplified as \( O(m^2n) \). It can be seen that
the computational complexity is closely related to the number of vertices and edges in
the graph. At the same time, the number of vertices and edges is directly related to the
number of UAVs and targets. In large-scale interception scenarios, solving a feasible task
assignment scheme for a large number of UAVs and targets will result in a large amount of computation time. Therefore, it is necessary to design an effective method to reduce the scale of the model.

Assuming that the large-scale task assignment model can be decomposed into \(N_{S}\) submodels, the UAV set and task set in the \(q^{ilt}(q=1,2,...,N_{S})\) submodel are separately \(U_{q}=\left\{U_{1},...,U_{N_{U_{q}}}\right\}\) and \(T_{q}=\left\{T_{1},...,T_{N_{T_{q}}}\right\}\). \(U_{N_{U_{q}}}\) and \(T_{N_{T_{q}}}\) represent the number of UAVs and tasks in the \(q^{ilt}\) submodel, respectively. Each submodel is solved by the centralized network flow model. Similar to (2), the maximum computational complexity of the submodel can be expressed as

\[B_{q}=O(m_{q}^{2}n_{q}).\] (3)

Then, the maximum computational complexity of the hierarchical task assignment model is

\[\begin{split}& B^{\prime}=O(\sum_{q=1}^{N_{S}}B_{q})=O(\sum_{q=1}^ {N_{S}}m_{q}^{2}n_{q})\leq O(\sum_{q=1}^{N_{S}}m_{q}^{2}n)\\ &\leq O((\sum_{q=1}^{N_{S}}m_{q})^{2}n)=O(m^{2}n)=B.\end{split}\] (4)

Obviously, the hierarchical task assignment model has less computational complexity than the original task assignment model through effective model decomposition. The required computation time is also reduced. However, the hierarchical scheme may lead to a suboptimal solution of the task assignment problem. Therefore, the core of the problem is to find a model decomposition method suitable for interception scenarios.

### Target Grouping Based on Feature Similarity Clustering

For targets in group-to-group interception scenarios, the individual behavior within the group is consistent, and the individuals are close together to form a relatively dense formation. Therefore, the targets with high similarity can be grouped based on the geometric and motion characteristics. In addition, we hope to limit the number of targets in each group, so the traditional feature similarity clustering algorithm [25] is improved in this section.

According to the sensor detection and fusion results, the state set of targets is assumed to be \(S(t)=\left\{s_{1}(t),s_{2}(t),...,s_{n}(t)\right\}\). Among them, \(s_{i}\) is the state of the \(i^{th}\) target. The time of data collection, target position, speed, and heading angle are all taken into account. The similarity matrices \(M_{d}\),\(M_{v}\),\(M_{\theta}\) are calculated according to the above information. The similarity matrices' calculations are carried out in (5) and (6).

(1) The similarity of the target position: According to the Euclidean distance between two targets \(d_{mn}\), the target position similarity is defined as

\[M_{d}(d_{mn})=\begin{cases}1,&d_{mn}\leq\alpha\\ e^{-k(d_{mn}-\alpha)},&d_{mn}>\alpha,\end{cases}\] (5)

where \(\alpha\) is the threshold, representing the allowed Euclidean distance between two targets.

(2) The similarity of target speed and heading angle: The similarity of the target speed is defined as

\[M_{v}(v_{mn})=\frac{\alpha_{2}-v_{mn}}{\alpha_{2}-\alpha_{1}},\,\,\,\alpha_{1 }\leq v_{mn}\leq\alpha_{2},\] (6)

Among them, \(v_{mn}\) is the absolute value of the velocity difference. We define the parameters \(\alpha_{1}\) and \(\alpha_{2}\) to measure the influence of the velocity difference. \(\alpha_{2}-\alpha_{1}\) represents the maximum allowed velocity difference and (6) is normalized based on it. Considering the cruising speed of the fixed-wing UAV, the values of \(\alpha_{1}\) and \(\alpha_{2}\) are taken as 5 m/s and 10 m/s, respectively. The speed similarity of the two targets is 1 when the speed difference is within 5 m/s, and 0 when the speed difference is greater than 10 m/s.

The heading angle similarity \( M_\theta(\theta_{mn}) \) calculation method is the same as that of the speed similarity, where the parameter values are 5 and 10. An undirected graph was constructed to form the target group based on the similarity between the target nodes. Target nodes with high similarity will form connected branches, but there may be isolated target points or subclusters. As shown in Figure 3, target 1 has low similarity with other targets and belongs to isolated nodes. The ideal clustering result is that the number of targets in each subcluster is relatively balanced. Too small or too large subclusters will weaken the clustering effect and further affect the performance of the task assignment algorithm.

Graph merge
Cluster 1
Cluster 2
Cluster 3
Target 1
A
B
C
Graph partition
Cluster 1
Cluster 2
Cluster 3
Target 1
C
Cluster 2
Cluster 3
Target 1
B
C

Figure 3. Feature similarity clustering with upper and lower bounds. (A) Clustering result with an isolated points; (B) Graph merge; (C) Graph partition.

Therefore, the upper and lower bounds for the number of targets in the subclusters are defined as \([b_i, b_u]\). For a subcluster whose number of targets is less than the lower bound, iteratively find the target \( j \) in other subclusters with the highest similarity with the subcluster, and classify the isolated targets into the cluster where the target \( j \) is located. After several iterations, the isolated targets with low similarity are classified into clusters, completing the graph merging process. At the same time, the algorithm performs graph partitioning for clusters whose number of targets exceeds the upper bounds. Considering interception characteristics, the number of UAVs in the divided subclusters should be balanced as much as possible, and neighboring UAVs should be divided into the same subcluster. Therefore, the linear deterministic greedy (LDG) algorithm is adopted to implement graph partitioning [26]. The algorithm uses a greedy algorithm to ensure a balanced load of nodes in each subgraph. Therefore, the LDG algorithm is suitable for graph partitioning in interception scenarios. In summary, the feature similarity clustering method with upper and lower bounds is given in Algorithm 1. The parameter \(\lambda\) measures the degree of similarity and is set to 0.7 in this paper.

**Algorithm 1 Feature Similarity Clustering with upper and lower bounds**

Input: \(M_{d},M_{v},M_{\theta},\lambda\)Output: target clusters: \(T_{1},T_{2},...\)

[1] \(M=M_{d}\cap M_{v}\cap M_{\theta}\)\(m_{ij}\) in \(M\)\(m_{ij}=sgn(m_{ij}-\lambda)\)\(i,j=find(m_{ij}-1)\)\(ind=argmin(i,j)\)\(T_{ind}=T_{ind}\cap\{j\}\)\(T_{1},T_{2},...\) in target clusters **size(\(T_{i}\)) \(\leq b_{i}\)**\(j_{m}=argmax_{i}(M_{ij}),\forall j\)**size(\(T_{j}\)) \(\geq b_{i}\)**\(T_{j_{m}}=T_{j_{m}}\cap T_{i}\)**size (\(T_{i}\)) \(\geq b_{u}\)**\(LDG(T_{i},c_{num})\)**

### UAV Assignment Based on Interception Advantage

UAVs have limited execution capabilities, and the requirements of targets in different subclusters are also different. In order to maximize the execution capability of the UAV and meet the target requirements, an interception advantage-based assignment algorithm is proposed to assign UAV teams to these subclusters.

Assuming that targets have been clustered into \(N_{S}\) target sets \(\{T_{1},T_{2},...,T_{N_{S}}\}\), the clustering centers are extracted as representatives of the entire subcluster. The quantity requirements for targets in these subclusters are defined in (7).

\[R_{n}=\left\{R^{n}_{1},R^{n}_{2},...,R^{n}_{N_{S}}\right\}.\] (7)

Among them, \(R^{n}_{j}\) represents the number of UAVs required by the \(j^{th}\) subcluster. The number of UAVs required \(R^{n}_{j}\) is solved using a proportional approach, where the weights \(\beta_{j}\) can be adjusted according to the priority of the target. In the interception scenario, a single UAV can intercept only one target. Therefore, the number of UAVs assigned should be greater than the number of targets. Based on this, the weights are constrained in (8). This paper simply conditions the number of targets in a subcluster to set the assignment weights.

\[\begin{array}{l}R^{n}_{j}=\beta_{j}\cdot N_{U}\\ \beta_{j}\geq\frac{N^{j}_{T}}{\sum_{i=1}^{N_{S}}N^{i}_{T},\forall j=1,2,...,N_{ S}\\ \sum_{i=1}^{N_{S}}\beta_{i}=1.\end{array}\] (8)

Based on the kinematic parameters detected by the radar system, the interception advantage is calculated to guide the assignment of UAVs.

The kinematic characteristics of the UAVs and targets are shown in Figure 4.

The relative velocity advantage is defined in (9).

\[D_{vij}(v_{i},v_{Tj})=\begin{cases}1,&v_{i}>v_{Tj}\\ \frac{v_{i}}{v_{Tj}},&0.5v_{Tj}\leq v_{i}<v_{Tj}\\ 0.1,&v_{i}<0.5v_{Tj}.\end{cases}\] (9)

The relative distance between the UAV and the target will also affect the interception efficiency due to the cost of the flight. Therefore, the distance between the UAV and the target is considered as a factor in the interception advantage.

\[D_{dij}(d_{rij})=\frac{2}{1+e^{d_{rij}-d_{0}}},\] (10)

where \(d_{0}\) is the threshold of the relative distance. A smaller weight will be given when the distance between the UAV and the target is greater than \(d_{0}\).

In this paper, the pursuit evasion interception mode is employed, and UAVs have lower speed than targets. When the speeds of the UAV and the target are on the same side as the line of sight, it is considered as the optimal interception angle. The advantage of the heading angle is defined as follows:

\[D_{\theta ij}(\theta_{i},\theta_{Tj})=\begin{cases}1-\frac{|\theta_{i}-\theta _{Tj}|}{\pi},&|\theta_{i}|<\frac{\pi}{2}\,\,\,|\theta_{Tj}|<\frac{\pi}{2}\\ 0.001,&others.\end{cases}\] (11)

If the heading angle advantage is slight, the interception advantage will be small despite the large speed and distance advantage. Therefore, the effect of heading angle on interception advantage is described by multiplication. Thus, the interception advantage is defined as

\[D_{ij}=\left[\lambda_{1}D_{vij}(v_{i},v_{Tj})+\lambda_{2}D_{dij}(d_{rij})\right] \cdot D_{\theta ij}(\theta_{i},\theta_{Tj}),\] (12)

Among them, \(\lambda_{1},\lambda_{2}\) are the weights and \(\lambda_{1}+\lambda_{2}=1\).

Based on the interception advantage, the auction-based algorithm [22, 27] is used to assign UAVs to the cluster. The interception advantage-based assignment of UAVs is given in Algorithm 2.

**Algorithm 2 Assignment of UAVs**

Input: \(R_{n}\),\(D\)

Output: \(N_{S}\) UAV teams: \(U_{1},U_{2},...,U_{N_{S}}\)

[1] \(U_{1}=\emptyset,...,U_{N_{S}}=\emptyset\)\(D\neq\emptyset\)\(j_{T}=argmaxR_{n}\)\(i_{U}=argmaxD_{ij_{T}}\)\(R^{in}_{j_{T}}=R^{n}_{j_{T}}-1\)\(D_{i_{U}}=0\)\(U_{j_{T}}=U_{j_{T}}\cap\{i_{U}\}\)

The large-scale task assignment model can be effectively decomposed into several small-scale and non-overlapping task assignment submodels using the proposed model decomposition method. Then, the network flow model is applied to solve these small-scale task assignment problems.

## 4 Evaluation Model and Task Assignment Method

### Evaluation Model Based on the Apollonius Circle

In order to describe the spatial location relationship between the UAVs and targets, the local coordinate system is established with zero-point as the origin. The coordinates of UAV \(i\) and target \(j\) are represented by \((x_{u},y_{u})\) and \((x_{t},y_{t})\), respectively. \(v_{u}\) and \(v_{t}\) are the maximum speeds of UAVs and targets, respectively. The velocity rate of the UAV and the target is defined as \(\lambda=v_{u}/v_{t}<1\). Assuming that both UAVs and the targets start moving at their maximum speeds, they will eventually meet at a point \(M=(x_{m},y_{m})\) in a finite time. The meeting point \(M\) satisfies (13).

\[\frac{\sqrt{(x_{u}-x_{m})^{2}+(y_{u}-y_{m})^{2}}}{\sqrt{(x_{t}-x_{m})^{2}+(y_{t }-y_{m})^{2}}}=\frac{v_{u}\cdot T}{v_{t}\cdot T}=\lambda.\] (13)

Then, (13) can be transformed into

\[(x_{m}-\frac{x_{u}-\lambda^{2}x_{t}}{1-\lambda^{2}})^{2}+(y_{m}- \frac{y_{u}-\lambda^{2}y_{t}}{1-\lambda^{2}})^{2}=\] \[\lambda^{2}((x_{u}-x_{t})^{2}+(y_{u}-y_{t})^{2})\] \[(1-\lambda^{2})^{2}.\]

It can be seen that \(M=(x_{m},y_{m})\) is on a circle shown in Figure 5a. The center of this circle can be expressed as (15), with the radius in (16). The circle is known as the Apollonius circle [28].

\[O=(\frac{x_{u}-\lambda^{2}x_{t}}{1-\lambda^{2}},\frac{y_{u}-\lambda^{2}y_{t}}{ 1-\lambda^{2}}).\] (15)

\[r=\frac{\lambda\sqrt{(x_{u}-x_{t})^{2}+(y_{u}-y_{t})^{2}}}{1-\lambda^{2}}.\] (16)

In Figure 5a, \(l_{1}\) and \(l_{2}\) are the tangent lines of the Apollonius circle from the target \(T\). If the flight path of the target is between the tangent lines \(l_{1}\) and \(l_{2}\), the UAV can move along the corresponding direction to capture the target. The capture point is on the Apollonius circle. Therefore, we define the angle \(\theta\) as the interceptable angle of the UAV, and the arc between the lines \(l_{1}\) and \(l_{2}\) can be defined as the interceptable area.

However, it is impractical for fixed-wing UAVs to reach the interceptable area in Figure 4(b) due to dynamic constraints. Therefore, the Apollonius circle suitable for fixed-wing UAVs is proposed, and the interceptable area should be redefined. As is shown in Figure 4(b), two lines \(TM_{1}\) and \(TM_{2}\) intersect the circle at \(M_{1}\) and \(M_{2}\), and the interceptable angle of the UAV can be represented by \(\theta^{\prime}=[\theta^{\prime}_{1},\theta^{\prime}_{2}]\). Compared with Figure 5(a), the orange part indicates the reduced interception area for fixed-wing UAVs. According to the sine rule, the interceptable angle for fixed-wing UAVs can be expressed as

\[\frac{M_{1}T}{sin\theta^{\prime}_{1}} =\frac{M_{1}U}{sin\theta_{1}}\] (17) \[\frac{M_{2}T}{sin\theta^{\prime}_{2}} =\frac{M_{2}U}{sin\theta_{2}}.\]

Then, the interceptable area of the fixed-wing UAV can be defined as

\[\begin{cases}\theta=\theta_{1}+\theta_{2}\\ \theta_{1}=arcsin(\lambda sin\theta^{\prime}_{1})\\ \theta_{2}=arcsin(\lambda sin\theta^{\prime}_{2}).\end{cases}\] (18)

Accordingly, the escapable direction of the target is defined as the maneuverable area \(\theta_{T}\). This maneuverable area \(\theta_{T}\) can be calculated based on the angular velocity \(\omega_{T}\) and the detection range \(L_{0}\) provided by the radar system.

Therefore, the ratio of the interceptable area of the UAV to the maneuverable area of the target can be defined as the the interception probability in (19). In other words, \(\theta_{ij}/\theta_{Tj}\) percent of the escapable directions of the target \(j\) are occupied by UAV \(i\).

\[p_{ij}=\frac{\theta_{ij}}{\theta_{Tj}}.\] (19)

If the interceptable area of the UAV can cover the maneuverable area of the target, the above interception probability is 1. Therefore, the goal of task assignment is to enable the UAV to cover the maneuverable area of the target as much as possible, i.e., to maximize the interception probability.

In addition, considering that the threat level of the target is different in the practical group-to-group interception scenario, UAVs need to be assigned according to the interception priority of the target. To enable the UAV with better interception performance to intercept the higher-threat target first, the target threat is evaluated to guide the task assignment phase. The position and maneuverability of the target are taken into account to evaluate the threat level.

(1) Target distance: The target distance is defined as the Euclidean distance between the target and the vital area. As is shown in Figure 6, the threat level of the target increases as the target distance decreases. Based on the effective distance of our defense system, the minimum allowable and maximum defense distances are defined as the thresholds for evaluating the threat level of the target distance. Thus, the evaluation function of target distance is

\[W_{1}(d)=\begin{cases}1,&d\leq d_{min}\\ \frac{d_{max}-d}{d_{max}-d_{min}},&d_{min}<d\leq d_{max}\\ 0,&d>d_{max}.\end{cases}\] (20)

(2) The maneuverability of the target: The speed of the target is taken into account to evaluate the threat priority of the target. Since the kinetic energy is proportional to the square of the speed, the square of the speed is used as an evaluation factor. The logarithm form of the factor is defined as (21).

\[W_{2}(v_{T})=ln(1+v_{T}^{2}).\] (21)

The weighted sum of the above factors is used to evaluate the threat level of the target.

\[W_{j}=\lambda_{3}W_{1}(d)+\lambda_{4}W_{2}(v_{T}),\] (22)

Among them, \(\lambda_{3},\lambda_{4}\) are the weights and \(\lambda_{3}+\lambda_{4}=1\). Note that the higher the threat level, the greater the need to intercept the target. The expected intercept priority also increases accordingly. Therefore, the threat level above is converted into the expected interception priority threshold.

\[P^{0}_{j}=\begin{cases}0.5,W_{j}\leq 0.5\\ W_{j},W_{j}\geq 0.5.\end{cases}\] (23)

Therefore, in large-scale interception scenarios, the specific modeling methods based on the Apollonius circle and the fixed-wing UAV dynamic model are designed to increase the validity and applicability of the evaluation model and accurately assess the situation. For the \(q^{th}\) submodel, the evaluation model is proposed as follows:

\[\max I_{q}=\sum_{j}^{N_{Tq}}\sum_{i}^{N_{Uq}}P^{0}_{j}\cdot x_{ij}p_{ij},\] (24)

subject to

\[\sum_{j=1}^{N_{Tq}}x_{ij}\leq 1\] (25)

\[1 \leq \sum_{i=1}^{N_{Uq}} x_{ij} \leq N_{min}\] (26)

\[\sum_{i=1}^{N_{Uq}} \sum_{j=1}^{N_{Tq}} x_{ij} = N_{Tq}\] (27)

\[x_{ij} = \{0,1\}, \forall i \in N_{Uq}, j \in N_{Tq};\] (28)

(25) shows that each UAV can intercept only one target. (26) represents that each target can be assigned to multiple UAVs. (27) indicates that all the targets should be assigned.

4.2. Task Assignment Based on Network Flow Model

Based on the above evaluation model, the task assignment phase aims to maximize the effectiveness of interception and satisfy the constraints of UAVs and targets. The task assignment problem for multi-UAV systems in group-to-group interception scenarios can be modeled as a network flow model with upper and lower flow bounds. Therefore, the task assignment minimum-cost maximum-flow algorithm (TAMM) in the previous work is adopted to solve the problem [29].

Since each UAV can only intercept one target, and each target must be executed by at least one UAV, the lower flow bounds of the network are defined. In addition, limited by the flight cost of UAVs, it is assumed that each target requires at most two UAVs to intercept, thus limiting the upper flow bounds of the network. The diagram of the network flow model is shown in Figure 7. UAVs and tasks are considered as vertices to form a two-layer network structure. To ensure the balance of the network flow, a virtual source point \(SS\) and a virtual sink point \(TT\) are introduced into the network. Among them, the tuples \(\langle B,C\rangle\) represent the cost and the capacity of the network. In the interception scenarios, the capacity function is represented by the number of targets that the UAV can intercept. The cost function is directly related to the interception effectiveness of the UAV. The network flow models are described according to the relationship between the number of tasks and UAVs.

(1) \(N_{U} \geq 2N_T\): UAVs can fully satisfy the need for interception, which allows UAVs to have a surplus. Targets are able to satisfy the conditions of interception by two UAVs. Therefore, only the flow bounds of the edges from the target vertices to the sink vertices \(E(V_{Tj}, V_i)\) are limited to \([1, 2]\).

(2) \(N_T \leq N_{U} < 2N_T\): All the UAVs are needed to participate in the intercept mission. Therefore, certain constraints are imposed on the capacity of the target vertices and the UAV vertices. The flow bounds of the edges from the source point to the UAV vertices \(E(V_s, V_{Ui})\) are limited to \([1, 1]\), while the flow bounds of the edges from the target vertices \(E(V_{Tj}, V_i)\) to the sink vertices are limited to \([1, 2]\).

The assignment scheme can be given by solving the minimum-cost maximum-flow problem. The Bellman–Ford algorithm is introduced to find a feasible solution.

4.3. Design of Interception Points for UAVs

When the target detects the UAV, it may take maneuverable strategies to escape. The warring parties will evade or pursue within the detection distance \( L_0 \). Therefore, the interception points are set on the circle with the target to be intercepted as the center and the detection range \( L_0 \) as the radius. The superposition of the interceptable area of the UAVs should cover the maneuver able area as much as possible. In addition, the overlap of the interceptable areas should be minimal to improve interception efficiency.

To facilitate the calculation, the polar coordinate system is established with the current position of the target as the origin and the current heading angle as the positive direction. Under the polar coordinate system, the interception scenario is shown in Figure 8.

Thus, the interception points in the polar coordinate system are

\[\begin{cases}
\rho_i = L_0 \\
\theta_i = \sum_{j=1}^{j=i} \theta_j + \theta_2^i.
\end{cases}\] (29)

Convert the above coordinate to the rectangular coordinate system as \((x_i', y_i') = (\rho_i cos \theta_i, \rho_i sin \theta_i)\). The rotation angle \(\theta_i\) of the coordinate system is
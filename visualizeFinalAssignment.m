function visualizeFinalAssignment(UAVs, Targets, all_assignments, interception_points, target_clusters, UAV_teams, battlefield_size)
% 可视化最终任务分配和拦截点
% 输入:
%   UAVs: 无人机结构体数组
%   Targets: 目标结构体数组
%   all_assignments: 所有子模型的任务分配结果
%   interception_points: 所有子模型的拦截点
%   target_clusters: 目标聚类结果
%   UAV_teams: 无人机团队分配结果
%   battlefield_size: 战场大小

figure;
hold on;

% 为每个聚类指定不同的颜色
colors = hsv(length(target_clusters));

% 为图例创建空句柄
h_target = [];
h_target_path = [];
h_uav = [];
h_interception = [];
h_uav_path = [];

% 绘制目标
for i = 1:length(target_clusters)
    cluster = target_clusters{i};
    for j = 1:length(cluster)
        target_id = cluster(j);
        
        % 绘制目标
        h = plot(Targets(target_id).x, Targets(target_id).y, 'o', 'MarkerSize', 8, ...
            'MarkerEdgeColor', colors(i,:), 'MarkerFaceColor', colors(i,:));
        
        if isempty(h_target)
            h_target = h;
        end
        
        % 绘制速度向量
        quiver(Targets(target_id).x, Targets(target_id).y, ...
            Targets(target_id).v*cos(Targets(target_id).theta), ...
            Targets(target_id).v*sin(Targets(target_id).theta), 0.5, 'Color', colors(i,:));
        
        % 添加目标标签
        text(Targets(target_id).x, Targets(target_id).y + 100, ['T' num2str(target_id)], ...
            'FontSize', 8, 'Color', colors(i,:));
    end
end

% 绘制无人机和拦截点
for i = 1:length(target_clusters)
    % 获取子模型的分配结果
    assignments = all_assignments{i};
    i_points = interception_points{i};
    
    % 获取子模型的无人机团队
    team = UAV_teams{i};
    
    % 绘制分配给该聚类的无人机
    for j = 1:length(team)
        uav_id = team(j);
        
        % 绘制无人机
        h = plot(UAVs(uav_id).x, UAVs(uav_id).y, '^', 'MarkerSize', 8, ...
            'MarkerEdgeColor', 'black', 'MarkerFaceColor', colors(i,:));
        
        if isempty(h_uav)
            h_uav = h;
        end
        
        % 添加无人机标签
        text(UAVs(uav_id).x, UAVs(uav_id).y + 100, ['U' num2str(uav_id)], ...
            'FontSize', 8, 'Color', 'black');
    end
    
    % 绘制拦截点和连线
    for j = 1:length(i_points)
        uav_id = i_points(j).uav_id;
        target_id = i_points(j).target_id;
        
        % 绘制拦截点
        h = plot(i_points(j).x, i_points(j).y, 'x', 'MarkerSize', 10, ...
            'MarkerEdgeColor', colors(i,:), 'LineWidth', 2);
        
        if isempty(h_interception)
            h_interception = h;
        end
        
        % 绘制无人机到拦截点的连线
        h = plot([UAVs(uav_id).x, i_points(j).x], [UAVs(uav_id).y, i_points(j).y], '--', ...
            'Color', colors(i,:), 'LineWidth', 1);
        
        if isempty(h_uav_path)
            h_uav_path = h;
        end
        
        % 绘制目标到拦截点的连线（表示目标可能的移动路径）
        h = plot([Targets(target_id).x, i_points(j).x], [Targets(target_id).y, i_points(j).y], ':', ...
            'Color', colors(i,:), 'LineWidth', 1);
        
        if isempty(h_target_path)
            h_target_path = h;
        end
    end
end

title('最终任务分配和拦截点');
% 使用句柄创建正确的图例
legend([h_target, h_uav, h_interception, h_uav_path, h_target_path], ...
    '目标', '无人机', '拦截点', '无人机路径', '目标可能路径');
grid on;
axis([0 battlefield_size 0 battlefield_size]);
hold off;

end 
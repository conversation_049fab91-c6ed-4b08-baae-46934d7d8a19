# 无人机生成模式使用指南

## 概述
本程序现在支持三种不同的无人机初始化模式，您可以根据不同的测试需求选择合适的模式。

## 如何选择模式

在 `main.m` 文件的第42行，修改 `UAV_generation_mode` 的值：

```matlab
UAV_generation_mode = 1;  % 修改这里的数字：1、2或3
```

## 三种模式详解

### 模式1：随机生成
- **设置值**: `UAV_generation_mode = 1`
- **特点**: 无人机随机分布在整个战场
- **适用场景**: 
  - 测试算法的鲁棒性
  - 验证算法在各种分布情况下的性能
  - 模拟突发情况下的无人机部署
- **统计特征**: 
  - 覆盖范围最大
  - 分散程度最高
  - 每次运行结果不同

### 模式2：固定生成（三基地部署）
- **设置值**: `UAV_generation_mode = 2`
- **特点**: 无人机从三个固定基地部署，呈圆形编队
- **基地位置**:
  - 基地1: (2000, 2000) - 左下方
  - 基地2: (2000, 6000) - 左上方  
  - 基地3: (5000, 2000) - 右下方
- **适用场景**:
  - 模拟实际军事部署
  - 调试和结果重现
  - 多基地协同作战测试
- **统计特征**:
  - 中等覆盖范围
  - 中等分散程度
  - 结果可重现

### 模式3：高度集中（螺旋编队）
- **设置值**: `UAV_generation_mode = 3`
- **特点**: 所有无人机从单一基地以螺旋形编队部署
- **基地位置**: (3000, 4000) - 战场中心偏左
- **适用场景**:
  - 测试密集编队的协同效果
  - 验证算法在高度集中场景下的性能
  - 模拟快速反应部队的部署
- **统计特征**:
  - 覆盖范围最小
  - 分散程度最低
  - 结果可重现

## 统计对比

| 模式 | 覆盖范围 | X坐标标准差 | Y坐标标准差 | 特点 |
|------|----------|-------------|-------------|------|
| 模式1 | ~8000×9000 | ~2300 | ~3300 | 最分散 |
| 模式2 | ~3200×4200 | ~1450 | ~1950 | 中等集中 |
| 模式3 | ~400×350 | ~110 | ~110 | 最集中 |

## 测试文件

程序提供了以下测试文件帮助您了解各种模式：

1. **test_uav_generation.m**: 对比模式1和模式2
2. **test_concentrated_uav.m**: 对比三种集中程度
3. **test_all_modes.m**: 快速测试所有三种模式

运行这些测试文件可以直观地看到不同模式的效果。

## 建议使用场景

### 算法开发阶段
- 使用**模式1**测试算法在各种情况下的鲁棒性
- 使用**模式2**进行调试，因为结果可重现

### 性能评估阶段
- 使用**模式2**评估多基地协同效果
- 使用**模式3**评估密集编队协同效果
- 使用**模式1**评估算法的平均性能

### 论文实验阶段
- 使用**模式2**获得稳定可重现的结果
- 使用**模式1**展示算法的通用性
- 使用**模式3**展示算法在极端情况下的表现

## 注意事项

1. **模式2和3使用固定随机种子**，确保结果可重现
2. **模式1每次运行结果不同**，适合统计分析
3. **所有模式都保持相同的无人机总数**和基本参数
4. **可以随时切换模式**，无需修改其他代码

## 快速开始

1. 打开 `main.m` 文件
2. 找到第42行的 `UAV_generation_mode = 2`
3. 修改数字为1、2或3
4. 运行程序查看效果

享受您的无人机任务分配实验！

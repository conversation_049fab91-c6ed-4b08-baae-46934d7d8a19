function interception_points = designInterceptionPoints(UAVs, Targets, assignments, L_0, v_u, v_t)
% 设计无人机拦截点
% 输入:
%   UAVs: 无人机结构体数组
%   Targets: 目标结构体数组
%   assignments: 任务分配结果，N_U × N_T的矩阵，1表示分配，0表示不分配
%   L_0: 探测范围
%   v_u: 无人机最大速度
%   v_t: 目标最大速度
% 输出:
%   interception_points: 拦截点坐标，结构体数组

N_U = length(UAVs);
N_T = length(Targets);
interception_points = struct('uav_id', {}, 'target_id', {}, 'x', {}, 'y', {});

% 对于每个目标，找到分配给它的所有无人机
for j = 1:N_T
    % 分配给目标j的无人机索引
    assigned_uavs = find(assignments(:, j));
    num_assigned = length(assigned_uavs);
    
    if num_assigned == 0
        continue;  % 没有无人机分配给该目标
    end
    
    % 计算目标j的可拦截角度
    theta_j = zeros(num_assigned, 1);
    for i = 1:num_assigned
        uav_id = assigned_uavs(i);
        
        % 计算无人机和目标之间的相对位置
        dx = Targets(j).x - UAVs(uav_id).x;
        dy = Targets(j).y - UAVs(uav_id).y;
        
        % 速度比率
        lambda = v_u / v_t;
        
        % 计算阿波罗尼圆的中心
        O_x = (UAVs(uav_id).x - lambda^2 * Targets(j).x) / (1 - lambda^2);
        O_y = (UAVs(uav_id).y - lambda^2 * Targets(j).y) / (1 - lambda^2);
        
        % 计算阿波罗尼圆的半径
        r = lambda * sqrt(dx^2 + dy^2) / (1 - lambda^2);
        
        % 计算目标到阿波罗尼圆中心的距离
        d_OT = sqrt((O_x - Targets(j).x)^2 + (O_y - Targets(j).y)^2);
        
        % 计算可拦截角度θ
        sin_theta_prime = r / d_OT;  % sin(θ')
        if sin_theta_prime > 1  % 当sin(θ')>1时，圆完全包含目标，θ'=π
            theta_prime = pi;
        else
            theta_prime = asin(sin_theta_prime);
        end
        
        % 根据公式(18)计算θ
        theta1 = asin(lambda * sin(theta_prime));
        theta2 = theta1;  % 由于对称性，θ1=θ2
        theta_j(i) = 2 * theta1;  % θ = θ1 + θ2
    end
    
    % 建立极坐标系，以目标位置为原点，目标朝向为正方向
    target_pos = [Targets(j).x, Targets(j).y];
    target_heading = Targets(j).theta;
    
    % 按照论文公式(29)计算拦截点
    % 将拦截点设置在以目标为中心，半径为L_0的圆周上
    for i = 1:num_assigned
        uav_id = assigned_uavs(i);
        
        % 根据无人机索引计算极坐标角度
        % 这里简化处理，均匀分布在目标的可拦截区域内
        if num_assigned == 1
            theta_i = 0;  % 单个无人机直接从正前方拦截
        else
            % 将无人机均匀分布在π的角度范围内（-π/2到π/2）
            theta_i = (i - 1) * pi / (num_assigned - 1) - pi/2;
        end
        
        % 将极坐标转换为直角坐标
        rho_i = L_0;
        x_prime = rho_i * cos(theta_i);
        y_prime = rho_i * sin(theta_i);
        
        % 将直角坐标旋转并平移到全局坐标系中
        rot_matrix = [cos(target_heading), -sin(target_heading); sin(target_heading), cos(target_heading)];
        pos_rotated = rot_matrix * [x_prime; y_prime];
        
        % 拦截点的全局坐标
        intercept_x = target_pos(1) + pos_rotated(1);
        intercept_y = target_pos(2) + pos_rotated(2);
        
        % 保存拦截点信息
        point = struct('uav_id', uav_id, 'target_id', j, 'x', intercept_x, 'y', intercept_y);
        interception_points(end+1) = point;
    end
end

end 
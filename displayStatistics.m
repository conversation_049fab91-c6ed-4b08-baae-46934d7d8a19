function displayStatistics(target_clusters, UAV_teams, all_assignments)
% 输出统计结果
% 输入:
%   target_clusters: 目标聚类结果
%   UAV_teams: 无人机团队分配结果
%   all_assignments: 所有子模型的任务分配结果

% 显示聚类统计信息
fprintf('======= 目标聚类统计 =======\n');
fprintf('总共聚类数量: %d\n', length(target_clusters));
for i = 1:length(target_clusters)
    fprintf('聚类 %d: 包含 %d 个目标\n', i, length(target_clusters{i}));
end
fprintf('\n');

% 显示无人机团队分配统计
fprintf('======= 无人机团队分配统计 =======\n');
total_uavs = 0;
for i = 1:length(UAV_teams)
    fprintf('团队 %d: 分配 %d 架无人机\n', i, length(UAV_teams{i}));
    total_uavs = total_uavs + length(UAV_teams{i});
end
fprintf('总共分配无人机: %d\n\n', total_uavs);

% 显示任务分配统计
fprintf('======= 任务分配统计 =======\n');
for i = 1:length(all_assignments)
    assignments = all_assignments{i};
    
    % 计算多对一分配情况（多个无人机拦截同一目标）
    targets_count = sum(assignments, 1);
    multi_uav_targets = sum(targets_count > 1);
    single_uav_targets = sum(targets_count == 1);
    no_uav_targets = sum(targets_count == 0);
    
    fprintf('子模型 %d:\n', i);
    fprintf('  - 单个无人机拦截的目标数量: %d\n', single_uav_targets);
    fprintf('  - 多个无人机拦截的目标数量: %d\n', multi_uav_targets);
    if no_uav_targets > 0
        fprintf('  - 未分配无人机的目标数量: %d (警告!)\n', no_uav_targets);
    end
    
    % 计算一对多分配情况（一个无人机拦截多个目标）
    uavs_count = sum(assignments, 2);
    multi_target_uavs = sum(uavs_count > 1);
    if multi_target_uavs > 0
        fprintf('  - 拦截多个目标的无人机数量: %d (警告! 每个无人机应只拦截一个目标)\n', multi_target_uavs);
    end
end
fprintf('\n');

% 计算总体任务分配效率
fprintf('======= 任务分配效率 =======\n');
total_targets = sum(cellfun(@length, target_clusters));
total_assigned_targets = 0;

for i = 1:length(all_assignments)
    assignments = all_assignments{i};
    targets_count = sum(assignments, 1);
    total_assigned_targets = total_assigned_targets + sum(targets_count > 0);
end

assignment_ratio = total_assigned_targets / total_targets * 100;
fprintf('目标分配率: %.2f%%\n', assignment_ratio);

% 计算多无人机拦截率
multi_uav_count = 0;
for i = 1:length(all_assignments)
    assignments = all_assignments{i};
    targets_count = sum(assignments, 1);
    multi_uav_count = multi_uav_count + sum(targets_count > 1);
end

multi_uav_ratio = multi_uav_count / total_targets * 100;
fprintf('多无人机拦截率: %.2f%%\n', multi_uav_ratio);

end 
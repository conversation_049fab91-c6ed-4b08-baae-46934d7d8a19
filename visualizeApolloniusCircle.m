function visualizeApolloniusCircle(UAV, Target, v_u, v_t)
% 可视化阿波罗尼斯圆动态变化
% 输入:
%   UAV: 无人机结构体
%   Target: 目标结构体
%   v_u: 无人机速度
%   v_t: 目标速度

% 速度比率
lambda = v_u / v_t;

% 创建图窗
figure('Name', '阿波罗尼斯圆验证');
hold on;

% 绘制无人机和目标初始位置
plot(UAV.x, UAV.y, 'r^', 'MarkerSize', 10, 'LineWidth', 2);
plot(Target.x, Target.y, 'bo', 'MarkerSize', 10, 'LineWidth', 2);
text(UAV.x, UAV.y+200, 'UAV', 'FontSize', 12);
text(Target.x, Target.y+200, 'Target', 'FontSize', 12);

% 绘制速度向量
quiver(UAV.x, UAV.y, v_u*cos(UAV.theta), v_u*sin(UAV.theta), 0, 'r', 'LineWidth', 2);
quiver(Target.x, Target.y, v_t*cos(Target.theta), v_t*sin(Target.theta), 0, 'b', 'LineWidth', 2);

% 计算阿波罗尼斯圆
dx = Target.x - UAV.x;
dy = Target.y - UAV.y;

% 计算阿波罗尼斯圆的中心
O_x = (UAV.x - lambda^2 * Target.x) / (1 - lambda^2);
O_y = (UAV.y - lambda^2 * Target.y) / (1 - lambda^2);

% 计算阿波罗尼斯圆的半径
r = lambda * sqrt(dx^2 + dy^2) / (1 - lambda^2);

% 绘制阿波罗尼斯圆
t = linspace(0, 2*pi, 100);
x_circle = O_x + r * cos(t);
y_circle = O_y + r * sin(t);
plot(x_circle, y_circle, 'g--', 'LineWidth', 2);

% 计算目标到阿波罗尼斯圆中心的连线
plot([Target.x, O_x], [Target.y, O_y], 'k-', 'LineWidth', 1);

% 计算从目标到阿波罗尼斯圆的切线
d_OT = sqrt((O_x - Target.x)^2 + (O_y - Target.y)^2);
sin_theta_prime = r / d_OT;
theta_prime = asin(sin_theta_prime);

% 目标朝向角
target_angle = atan2(O_y - Target.y, O_x - Target.x);

% 计算切线角度
tangent_angle1 = target_angle + theta_prime;
tangent_angle2 = target_angle - theta_prime;

% 绘制切线（标记可拦截区域）
line_length = 3000;
x1 = Target.x + line_length * cos(tangent_angle1);
y1 = Target.y + line_length * sin(tangent_angle1);
x2 = Target.x + line_length * cos(tangent_angle2);
y2 = Target.y + line_length * sin(tangent_angle2);
plot([Target.x, x1], [Target.y, y1], 'm-', 'LineWidth', 1.5);
plot([Target.x, x2], [Target.y, y2], 'm-', 'LineWidth', 1.5);

% 绘制固定翼无人机可拦截区域
theta1 = asin(lambda * sin(theta_prime));
theta2 = theta1;
tangent_angle1_fixed = target_angle + theta1;
tangent_angle2_fixed = target_angle - theta2;
x1_fixed = Target.x + line_length * cos(tangent_angle1_fixed);
y1_fixed = Target.y + line_length * sin(tangent_angle1_fixed);
x2_fixed = Target.x + line_length * cos(tangent_angle2_fixed);
y2_fixed = Target.y + line_length * sin(tangent_angle2_fixed);
plot([Target.x, x1_fixed], [Target.y, y1_fixed], 'c-', 'LineWidth', 2);
plot([Target.x, x2_fixed], [Target.y, y2_fixed], 'c-', 'LineWidth', 2);

% 标记减少的拦截区域（动态约束影响）
patch([Target.x, x1, x1_fixed, Target.x], [Target.y, y1, y1_fixed, Target.y], [1 0.8 0.6], 'EdgeColor', 'none', 'FaceAlpha', 0.5);
patch([Target.x, x2, x2_fixed, Target.x], [Target.y, y2, y2_fixed, Target.y], [1 0.8 0.6], 'EdgeColor', 'none', 'FaceAlpha', 0.5);

% 在圆上标记拦截点
theta_intercept = (tangent_angle1_fixed + tangent_angle2_fixed) / 2;
x_intercept = Target.x + line_length * cos(theta_intercept);
y_intercept = Target.y + line_length * sin(theta_intercept);
plot(x_intercept, y_intercept, 'mx', 'MarkerSize', 12, 'LineWidth', 2);

% 添加标题和图例
title('阿波罗尼斯圆与拦截区域验证', 'FontSize', 14);
legend('无人机(红)', '目标(蓝)', '', '', '阿波罗尼斯圆', '', '理想拦截边界', '', '固定翼拦截边界', '', '', '减少的拦截区域', '拦截点');
grid on;
axis equal;

% 添加说明文字
text(O_x, O_y+100, '圆心', 'FontSize', 10);
text(r+O_x, O_y, ['半径: ' num2str(r, '%.1f') 'm'], 'FontSize', 10);
text(Target.x + 400, Target.y + 400, ['速度比λ = ' num2str(lambda, '%.2f')], 'FontSize', 12);
text(Target.x + 400, Target.y + 200, ['拦截角θ = ' num2str(2*theta1*180/pi, '%.1f') '°'], 'FontSize', 12);

hold off;

end 
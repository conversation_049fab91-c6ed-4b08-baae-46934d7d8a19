function results = performanceAnalysis(target_clusters, UAV_teams, all_assignments, UAVs, Targets, execution_time)
% 性能分析模块 - 计算和分析算法性能指标
% 输入:
%   target_clusters: 目标聚类结果
%   UAV_teams: 无人机团队分配结果
%   all_assignments: 所有子模型的任务分配结果
%   UAVs: 无人机结构体数组
%   Targets: 目标结构体数组
%   execution_time: 算法执行时间（可选）
% 输出:
%   results: 包含所有性能指标的结构体

if nargin < 6
    execution_time = 0;
end

results = struct();

%% 基本统计信息
results.basic.num_uavs = length(UAVs);
results.basic.num_targets = length(Targets);
results.basic.num_clusters = length(target_clusters);
results.basic.execution_time = execution_time;

%% 聚类质量分析
fprintf('=== 聚类质量分析 ===\n');
cluster_sizes = cellfun(@length, target_clusters);
results.clustering.cluster_sizes = cluster_sizes;
results.clustering.min_cluster_size = min(cluster_sizes);
results.clustering.max_cluster_size = max(cluster_sizes);
results.clustering.avg_cluster_size = mean(cluster_sizes);
results.clustering.cluster_balance = std(cluster_sizes) / mean(cluster_sizes);  % 越小越均衡

fprintf('聚类数量: %d\n', results.basic.num_clusters);
fprintf('聚类大小: 最小=%d, 最大=%d, 平均=%.1f\n', ...
    results.clustering.min_cluster_size, results.clustering.max_cluster_size, results.clustering.avg_cluster_size);
fprintf('聚类均衡度: %.3f (越小越均衡)\n', results.clustering.cluster_balance);

%% 任务分配效率分析
fprintf('\n=== 任务分配效率分析 ===\n');
total_assigned_targets = 0;
total_multi_uav_targets = 0;
total_single_uav_targets = 0;
total_unassigned_targets = 0;

for q = 1:length(all_assignments)
    assignments = all_assignments{q};
    targets_count = sum(assignments, 1);
    
    assigned_targets = sum(targets_count > 0);
    multi_uav_targets = sum(targets_count > 1);
    single_uav_targets = sum(targets_count == 1);
    unassigned_targets = sum(targets_count == 0);
    
    total_assigned_targets = total_assigned_targets + assigned_targets;
    total_multi_uav_targets = total_multi_uav_targets + multi_uav_targets;
    total_single_uav_targets = total_single_uav_targets + single_uav_targets;
    total_unassigned_targets = total_unassigned_targets + unassigned_targets;
end

results.assignment.assignment_rate = total_assigned_targets / results.basic.num_targets;
results.assignment.multi_uav_rate = total_multi_uav_targets / results.basic.num_targets;
results.assignment.single_uav_rate = total_single_uav_targets / results.basic.num_targets;
results.assignment.unassigned_rate = total_unassigned_targets / results.basic.num_targets;

fprintf('目标分配率: %.2f%% (%d/%d)\n', results.assignment.assignment_rate*100, total_assigned_targets, results.basic.num_targets);
fprintf('多无人机拦截率: %.2f%% (%d/%d)\n', results.assignment.multi_uav_rate*100, total_multi_uav_targets, results.basic.num_targets);
fprintf('单无人机拦截率: %.2f%% (%d/%d)\n', results.assignment.single_uav_rate*100, total_single_uav_targets, results.basic.num_targets);
if total_unassigned_targets > 0
    fprintf('未分配目标: %.2f%% (%d/%d) ⚠️\n', results.assignment.unassigned_rate*100, total_unassigned_targets, results.basic.num_targets);
end

%% 资源利用率分析
fprintf('\n=== 资源利用率分析 ===\n');
total_used_uavs = 0;
for q = 1:length(all_assignments)
    assignments = all_assignments{q};
    used_uavs = sum(sum(assignments, 2) > 0);
    total_used_uavs = total_used_uavs + used_uavs;
end

results.resource.uav_utilization = total_used_uavs / results.basic.num_uavs;
results.resource.uav_target_ratio = results.basic.num_uavs / results.basic.num_targets;

fprintf('无人机利用率: %.2f%% (%d/%d)\n', results.resource.uav_utilization*100, total_used_uavs, results.basic.num_uavs);
fprintf('无人机目标比: %.2f:1\n', results.resource.uav_target_ratio);

%% 威胁覆盖分析
fprintf('\n=== 威胁覆盖分析 ===\n');
threat_levels = [Targets.threat];
[sorted_threats, threat_indices] = sort(threat_levels, 'descend');

% 分析高威胁目标的覆盖情况
high_threat_count = ceil(0.3 * results.basic.num_targets);  % 前30%为高威胁
high_threat_indices = threat_indices(1:high_threat_count);

high_threat_covered = 0;
high_threat_multi_covered = 0;

for q = 1:length(all_assignments)
    assignments = all_assignments{q};
    cluster_targets = target_clusters{q};
    
    for i = 1:length(cluster_targets)
        target_id = cluster_targets(i);
        if ismember(target_id, high_threat_indices)
            coverage = sum(assignments(:, i));
            if coverage > 0
                high_threat_covered = high_threat_covered + 1;
            end
            if coverage > 1
                high_threat_multi_covered = high_threat_multi_covered + 1;
            end
        end
    end
end

results.threat.high_threat_coverage = high_threat_covered / high_threat_count;
results.threat.high_threat_multi_coverage = high_threat_multi_covered / high_threat_count;

fprintf('高威胁目标覆盖率: %.2f%% (%d/%d)\n', results.threat.high_threat_coverage*100, high_threat_covered, high_threat_count);
fprintf('高威胁目标多重覆盖率: %.2f%% (%d/%d)\n', results.threat.high_threat_multi_coverage*100, high_threat_multi_covered, high_threat_count);

%% 算法效率分析
if execution_time > 0
    fprintf('\n=== 算法效率分析 ===\n');
    results.efficiency.time_per_target = execution_time / results.basic.num_targets;
    results.efficiency.time_per_uav = execution_time / results.basic.num_uavs;
    
    fprintf('总执行时间: %.3f 秒\n', execution_time);
    fprintf('每目标处理时间: %.3f 秒\n', results.efficiency.time_per_target);
    fprintf('每无人机处理时间: %.3f 秒\n', results.efficiency.time_per_uav);
end

%% 综合评分
fprintf('\n=== 综合性能评分 ===\n');
% 计算综合评分（0-100分）
assignment_score = results.assignment.assignment_rate * 30;  % 分配率权重30%
multi_uav_score = results.assignment.multi_uav_rate * 25;    % 多无人机拦截率权重25%
utilization_score = results.resource.uav_utilization * 20;   % 资源利用率权重20%
threat_score = results.threat.high_threat_coverage * 15;     % 高威胁覆盖权重15%
balance_score = (1 - min(1, results.clustering.cluster_balance)) * 10;  % 聚类均衡权重10%

results.overall.score = assignment_score + multi_uav_score + utilization_score + threat_score + balance_score;

fprintf('综合评分: %.1f/100\n', results.overall.score);
fprintf('  - 目标分配: %.1f/30\n', assignment_score);
fprintf('  - 多机协同: %.1f/25\n', multi_uav_score);
fprintf('  - 资源利用: %.1f/20\n', utilization_score);
fprintf('  - 威胁覆盖: %.1f/15\n', threat_score);
fprintf('  - 聚类均衡: %.1f/10\n', balance_score);

% 性能等级评定
if results.overall.score >= 90
    grade = 'A+ (优秀)';
elseif results.overall.score >= 80
    grade = 'A (良好)';
elseif results.overall.score >= 70
    grade = 'B (中等)';
elseif results.overall.score >= 60
    grade = 'C (及格)';
else
    grade = 'D (需改进)';
end

fprintf('性能等级: %s\n', grade);
results.overall.grade = grade;

end

function P = calculateInterceptionProbability(UAVs, Targets, v_u, v_t, L_0)
% 基于阿波罗尼圆计算拦截概率，严格按照论文公式
% 输入:
%   UAVs: 无人机结构体数组
%   Targets: 目标结构体数组
%   v_u: 无人机最大速度
%   v_t: 目标最大速度
%   L_0: 探测范围
% 输出:
%   P: 拦截概率矩阵，N_U × N_T

N_U = length(UAVs);
N_T = length(Targets);
P = zeros(N_U, N_T);

% 速度比率
lambda = v_u / v_t;

% 假设目标的可机动区域角度（基于雷达系统提供的信息）
% 根据论文默认设置为π/2
theta_T = repmat(pi/2, 1, N_T);  % 论文中的标准设置

for i = 1:N_U
    for j = 1:N_T
        % 计算无人机和目标之间的相对位置
        dx = Targets(j).x - UAVs(i).x;
        dy = Targets(j).y - UAVs(i).y;
        
        % 如果距离为0，无法形成阿波罗尼圆，则概率为0
        if dx == 0 && dy == 0
            P(i, j) = 0;
            continue;
        end
        
        % 计算阿波罗尼圆的中心（公式17）
        O_x = (UAVs(i).x - lambda^2 * Targets(j).x) / (1 - lambda^2);
        O_y = (UAVs(i).y - lambda^2 * Targets(j).y) / (1 - lambda^2);
        
        % 计算阿波罗尼圆的半径（公式17）
        r = lambda * sqrt(dx^2 + dy^2) / (1 - lambda^2);
        
        % 计算目标到阿波罗尼圆中心的距离
        d_OT = sqrt((O_x - Targets(j).x)^2 + (O_y - Targets(j).y)^2);
        
        % 计算可拦截角度θ（公式18）
        sin_theta_prime = r / d_OT;  % sin(θ')
        if sin_theta_prime > 1  % 当sin(θ')>1时，圆完全包含目标，θ'=π
            theta_prime = pi;
        else
            theta_prime = asin(sin_theta_prime);
        end
        
        % 根据公式(18)计算θ
        theta1 = asin(lambda * sin(theta_prime));
        theta2 = theta1;  % 由于对称性，θ1=θ2
        theta_ij = 2 * theta1;  % θ = θ1 + θ2
        
        % 计算拦截概率（公式24）
        % p_ij是无人机i拦截目标j的拦截概率，取值范围[0,1]
        p_ij = min(1, theta_ij / theta_T(j));
        
        % 根据期望拦截优先级阈值调整拦截概率（公式24中的P^0_j）
        % 这里使用目标的威胁级别作为期望拦截优先级阈值
        P_0_j = max(0.5, Targets(j).threat);
        
        % 最终拦截概率（公式24）
        P(i, j) = P_0_j * p_ij;
    end
end

end 
%% 测试聚类算法
clear;
clc;

% 参数设置
N_T = 30;  % 目标数量
battlefield_size = 10000;
v_t = 70;
alpha = 1000;
b_l = 3;
b_u = 12;
lambda = 0.1;  % 适中的相似度阈值

% 初始化目标
Targets = struct('x', {}, 'y', {}, 'v', {}, 'theta', {}, 'threat', {});

% 创建结构化的目标分布，形成明显的聚类
% 创建3个目标群组
group1_center = [2000, 2000];  % 第一组中心
group2_center = [6000, 6000];  % 第二组中心
group3_center = [8000, 2000];  % 第三组中心

targets_per_group = floor(N_T / 3);
remaining_targets = N_T - 3 * targets_per_group;

target_idx = 1;

% 第一组目标（紧密聚集）
for i = 1:targets_per_group
    Targets(target_idx).x = group1_center(1) + 500 * (rand() - 0.5);
    Targets(target_idx).y = group1_center(2) + 500 * (rand() - 0.5);
    Targets(target_idx).v = 60 + 10 * (rand() - 0.5);  % 相似速度
    Targets(target_idx).theta = pi/4 + 0.2 * (rand() - 0.5);  % 相似方向
    Targets(target_idx).threat = rand();
    target_idx = target_idx + 1;
end

% 第二组目标（紧密聚集）
for i = 1:targets_per_group
    Targets(target_idx).x = group2_center(1) + 500 * (rand() - 0.5);
    Targets(target_idx).y = group2_center(2) + 500 * (rand() - 0.5);
    Targets(target_idx).v = 70 + 10 * (rand() - 0.5);  % 相似速度
    Targets(target_idx).theta = 3*pi/4 + 0.2 * (rand() - 0.5);  % 相似方向
    Targets(target_idx).threat = rand();
    target_idx = target_idx + 1;
end

% 第三组目标（紧密聚集）
for i = 1:targets_per_group
    Targets(target_idx).x = group3_center(1) + 500 * (rand() - 0.5);
    Targets(target_idx).y = group3_center(2) + 500 * (rand() - 0.5);
    Targets(target_idx).v = 50 + 10 * (rand() - 0.5);  % 相似速度
    Targets(target_idx).theta = -pi/4 + 0.2 * (rand() - 0.5);  % 相似方向
    Targets(target_idx).threat = rand();
    target_idx = target_idx + 1;
end

% 剩余的目标随机分布
for i = 1:remaining_targets
    Targets(target_idx).x = rand() * battlefield_size;
    Targets(target_idx).y = rand() * battlefield_size;
    Targets(target_idx).v = v_t * (0.8 + rand() * 0.4);
    Targets(target_idx).theta = rand() * 2 * pi;
    Targets(target_idx).threat = rand();
    target_idx = target_idx + 1;
end

% 计算相似度矩阵
disp('计算相似度矩阵...');
[M_d, M_v, M_theta] = calculateSimilarityMatrices(Targets, alpha);

% 执行聚类
disp('执行聚类...');
target_clusters = featureSimilarityClustering(M_d, M_v, M_theta, lambda, b_l, b_u, Targets);

disp('聚类测试完成');

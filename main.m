%% 多无人机系统大规模群对群拦截场景中的层次化任务分配
% 基于论文：Hierarchical Task Assignment for Multi-UAV System in Large-Scale Group-to-Group Interception Scenarios
clear;
clc;
close all;

%% 参数设置
% UAV与目标数量 - 与论文5.1节保持一致，但调整无人机数量以实现80%多无人机拦截率
N_U = 54;  % 无人机数量（调整为论文原数量+14，以实现80%多无人机拦截率）
N_T = 30;  % 目标数量（恢复为论文中的值）

% 场景参数
battlefield_size = 10000;  % 战场大小(m)
v_u = 50;  % UAV最大速度(m/s)
v_t = 70;  % 目标最大速度(m/s)
L_0 = 2000;  % 探测范围(m)
min_radius = 20;  % 最小转弯半径(m)

% 聚类参数 - 符合论文描述
alpha = 1000;  % 允许的欧几里德距离阈值
b_l = 3;  % 聚类下界（论文中的边界限制约束）
b_u = 12;  % 聚类上界（论文中的边界限制约束）
lambda = 0.7;  % 相似度阈值，论文原值，调整以获得更好的聚类效果

% 权重参数
lambda1 = 0.7;  % 速度优势权重
lambda2 = 0.3;  % 距离优势权重
lambda3 = 0.6;  % 目标距离威胁权重
lambda4 = 0.4;  % 目标机动性威胁权重

% 仿真参数
d_min = 3000;  % 最小允许距离
d_max = 8000;  % 最大防御距离

%% 初始化UAV和目标位置、速度和朝向
% ========== 无人机生成方式选择 ==========
% 修改下面的值来选择无人机生成方式：
% 1 - 随机生成（每次运行结果不同，适合测试算法鲁棒性）
% 2 - 固定生成（三基地部署，结果可重现，便于调试和对比）
% 3 - 高度集中（单基地螺旋编队，测试密集协同效果）
UAV_generation_mode = 1;  % <-- 在这里修改：1为随机，2为固定，3为高度集中
% =====================================

UAVs = struct('x', {}, 'y', {}, 'v', {}, 'theta', {});

if UAV_generation_mode == 1
    % 方式1：随机生成UAV的初始位置、速度和朝向
    disp('使用随机生成模式初始化无人机');
    for i = 1:N_U
        UAVs(i).x = rand() * battlefield_size;
        UAVs(i).y = rand() * battlefield_size;
        UAVs(i).v = v_u * (0.9 + rand() * 0.2);  % 速度在0.9v_u到1.1v_u之间随机
        UAVs(i).theta = rand() * 2 * pi;  % 朝向在0到2π之间随机
    end

elseif UAV_generation_mode == 2
    % 方式2：固定生成UAV的初始位置、速度和朝向
    disp('使用固定生成模式初始化无人机');

    % 设置随机种子以确保结果可重现
    rng(42);  % 使用固定种子

    % 定义三个固定的无人机基地位置（更集中的部署）
    base1_x = 2000;  base1_y = 2000;  % 左下基地
    base2_x = 2000;  base2_y = 6000;  % 左上基地
    base3_x = 5000;  base3_y = 2000;  % 右下基地

    % 计算每个基地的无人机数量
    uavs_per_base = floor(N_U / 3);
    remaining_uavs = N_U - 3 * uavs_per_base;

    uav_idx = 1;

    % 基地1部署（左下，更紧密的圆形编队）
    for i = 1:uavs_per_base
        angle = (i-1) * 2*pi / uavs_per_base;  % 均匀分布在圆周上
        radius = 80 + 40 * (i-1) / uavs_per_base;  % 缩小半径范围，更集中
        UAVs(uav_idx).x = base1_x + radius * cos(angle);
        UAVs(uav_idx).y = base1_y + radius * sin(angle);
        UAVs(uav_idx).v = v_u * (0.95 + 0.1 * sin(angle));  % 轻微速度变化
        UAVs(uav_idx).theta = angle + pi/4;  % 统一朝向右上方
        uav_idx = uav_idx + 1;
    end

    % 基地2部署（左上，更紧密的圆形编队）
    for i = 1:uavs_per_base
        angle = (i-1) * 2*pi / uavs_per_base;
        radius = 80 + 40 * (i-1) / uavs_per_base;  % 缩小半径范围
        UAVs(uav_idx).x = base2_x + radius * cos(angle);
        UAVs(uav_idx).y = base2_y + radius * sin(angle);
        UAVs(uav_idx).v = v_u * (0.95 + 0.1 * cos(angle));
        UAVs(uav_idx).theta = angle - pi/4;  % 统一朝向右下方
        uav_idx = uav_idx + 1;
    end

    % 基地3部署（右下，更紧密的圆形编队）
    for i = 1:(uavs_per_base + remaining_uavs)  % 剩余无人机分配给基地3
        angle = (i-1) * 2*pi / (uavs_per_base + remaining_uavs);
        radius = 80 + 60 * (i-1) / (uavs_per_base + remaining_uavs);  % 稍大一些容纳更多无人机
        UAVs(uav_idx).x = base3_x + radius * cos(angle);
        UAVs(uav_idx).y = base3_y + radius * sin(angle);
        UAVs(uav_idx).v = v_u * (0.9 + 0.2 * abs(sin(angle)));
        UAVs(uav_idx).theta = angle + pi/2;  % 统一朝向上方
        uav_idx = uav_idx + 1;
    end

    % 重置随机种子（避免影响后续随机过程）
    rng('shuffle');

elseif UAV_generation_mode == 3
    % 方式3：高度集中生成（单基地螺旋编队）
    disp('使用高度集中模式初始化无人机（螺旋编队）');

    % 设置随机种子以确保结果可重现
    rng(42);

    % 单一基地位置（战场中心偏左，便于拦截各方向目标）
    center_x = battlefield_size * 0.3;  % 30%位置
    center_y = battlefield_size * 0.4;  % 40%位置

    for i = 1:N_U
        % 使用螺旋形排列，确保高度集中但避免碰撞
        spiral_angle = i * pi/3;  % 螺旋角度
        spiral_radius = 50 + i * 12;  % 螺旋半径，紧密但有序

        UAVs(i).x = center_x + spiral_radius * cos(spiral_angle);
        UAVs(i).y = center_y + spiral_radius * sin(spiral_angle);
        UAVs(i).v = v_u * (0.95 + 0.1 * sin(spiral_angle));  % 轻微速度变化
        UAVs(i).theta = spiral_angle + pi/2;  % 基本朝向，便于快速展开
    end

    % 重置随机种子
    rng('shuffle');

else
    error('无效的无人机生成模式！请设置UAV_generation_mode为1（随机）、2（固定）或3（高度集中）');
end

% 创建目标，分布方式符合论文图3
Targets = struct('x', {}, 'y', {}, 'v', {}, 'theta', {}, 'threat', {});

% 定义战场中央为原点
center_x = battlefield_size / 2;
center_y = battlefield_size / 2;

% 集群1：左侧紧密集群（对应论文图3左下方集群）
cluster1_count = 8;
cluster1_center_x = center_x - 3000;
cluster1_center_y = center_y + 2000;
cluster1_radius = 500;
for i = 1:cluster1_count
    angle = rand() * 2 * pi;
    r = sqrt(rand()) * cluster1_radius;
    Targets(i).x = cluster1_center_x + r * cos(angle);
    Targets(i).y = cluster1_center_y + r * sin(angle);
    Targets(i).v = v_t * (0.9 + 0.1 * rand());
    Targets(i).theta = 3*pi/4 + 0.3 * randn();  % 向左上方运动
end

% 集群2：右侧集群（对应论文图3右侧集群）
cluster2_count = 7;
cluster2_center_x = center_x + 2500;
cluster2_center_y = center_y + 0;
cluster2_radius = 600;
for i = 1:cluster2_count
    angle = rand() * 2 * pi;
    r = sqrt(rand()) * cluster2_radius;
    Targets(cluster1_count + i).x = cluster2_center_x + r * cos(angle);
    Targets(cluster1_count + i).y = cluster2_center_y + r * sin(angle);
    Targets(cluster1_count + i).v = v_t * (0.9 + 0.1 * rand());
    Targets(cluster1_count + i).theta = pi + 0.4 * randn();  % 向左方运动
end

% 集群3：中下方集群（对应论文图3中下方集群）
cluster3_count = 9;
cluster3_center_x = center_x - 500;
cluster3_center_y = center_y - 2500;
cluster3_radius = 700;
for i = 1:cluster3_count
    angle = rand() * 2 * pi;
    r = sqrt(rand()) * cluster3_radius;
    idx = cluster1_count + cluster2_count + i;
    Targets(idx).x = cluster3_center_x + r * cos(angle);
    Targets(idx).y = cluster3_center_y + r * sin(angle);
    Targets(idx).v = v_t * (0.9 + 0.1 * rand());
    Targets(idx).theta = pi/4 + 0.4 * randn();  % 向右上方运动
end

% 集群4：上方小集群（对应论文图3上方小集群）
cluster4_count = 4;
cluster4_center_x = center_x - 500;
cluster4_center_y = center_y + 3000;
cluster4_radius = 400;
for i = 1:cluster4_count
    angle = rand() * 2 * pi;
    r = sqrt(rand()) * cluster4_radius;
    idx = cluster1_count + cluster2_count + cluster3_count + i;
    Targets(idx).x = cluster4_center_x + r * cos(angle);
    Targets(idx).y = cluster4_center_y + r * sin(angle);
    Targets(idx).v = v_t * (0.9 + 0.1 * rand());
    Targets(idx).theta = -pi/8 + 0.3 * randn();  % 略微向右下方运动
end

% 添加两个孤立点，符合论文图3
% 孤立点1
isolated1_idx = cluster1_count + cluster2_count + cluster3_count + cluster4_count + 1;
Targets(isolated1_idx).x = center_x + 1500;
Targets(isolated1_idx).y = center_y - 2500;
Targets(isolated1_idx).v = v_t * 0.9;
Targets(isolated1_idx).theta = 3*pi/4;  % 向左上方运动

% 孤立点2
isolated2_idx = isolated1_idx + 1;
Targets(isolated2_idx).x = center_x + 3500;
Targets(isolated2_idx).y = center_y + 2500;
Targets(isolated2_idx).v = v_t * 0.95;
Targets(isolated2_idx).theta = -pi/2;  % 向下方运动

% 确保总共有N_T个目标
assert(cluster1_count + cluster2_count + cluster3_count + cluster4_count + 2 == N_T, ...
       '目标总数不匹配设定值');

% 计算所有目标的威胁度
for i = 1:N_T
    % 计算到防御中心的距离
    d = sqrt((Targets(i).x - center_x)^2 + (Targets(i).y - center_y)^2);
    
    % 计算目标威胁度
    W1 = calculateDistanceThreat(d, d_min, d_max);
    W2 = log(1 + Targets(i).v^2);
    Targets(i).threat = lambda3 * W1 + lambda4 * W2;
end

%% 可视化初始UAV和目标分布
figure;
hold on;
% 绘制UAV
h_uav = [];
for i = 1:N_U
    h = plot(UAVs(i).x, UAVs(i).y, 'b^', 'MarkerSize', 8);
    if i == 1
        h_uav = h;
    end
    % 绘制速度向量
    quiver(UAVs(i).x, UAVs(i).y, UAVs(i).v*cos(UAVs(i).theta), UAVs(i).v*sin(UAVs(i).theta), 0.5, 'b');
end
% 绘制目标
h_target = [];
for i = 1:N_T
    h = plot(Targets(i).x, Targets(i).y, 'ro', 'MarkerSize', 8, 'MarkerFaceColor', 'r');
    if i == 1
        h_target = h;
    end
    % 绘制速度向量
    quiver(Targets(i).x, Targets(i).y, Targets(i).v*cos(Targets(i).theta), Targets(i).v*sin(Targets(i).theta), 0.5, 'r');
end
% 绘制防御中心
h_center = plot(center_x, center_y, 'ks', 'MarkerSize', 12, 'MarkerFaceColor', 'k');
title('初始UAV和目标分布');
legend([h_uav, h_target, h_center], 'UAV', '目标', '防御中心');
grid on;
axis([0 battlefield_size 0 battlefield_size]);
hold off;

%% 步骤1：基于特征相似性聚类的目标分组
% 计算目标之间的相似度矩阵
[M_d, M_v, M_theta] = calculateSimilarityMatrices(Targets, alpha);

% 执行特征相似性聚类
target_clusters = featureSimilarityClustering(M_d, M_v, M_theta, lambda, b_l, b_u, Targets);

% 可视化目标聚类结果
visualizeTargetClusters(Targets, target_clusters, battlefield_size);

% 添加新的可视化函数，以多角度展示聚类效果
visualizeClusteringProcess(Targets, target_clusters, battlefield_size);

%% 步骤2：基于拦截优势的无人机分配
% 计算拦截优势矩阵
D = calculateInterceptionAdvantage(UAVs, target_clusters, lambda1, lambda2, Targets);

% 为每个目标簇分配无人机团队
UAV_teams = assignUAVs(D, target_clusters, N_U);

% 可视化无人机团队分配结果
visualizeUAVAssignment(UAVs, Targets, target_clusters, UAV_teams, battlefield_size);

%% 步骤3：基于阿波罗尼圆的评估模型
% 对每个子模型进行评估和任务分配
% 预分配数组以提高性能
num_clusters = length(target_clusters);
all_assignments = cell(1, num_clusters);
interception_points = cell(1, num_clusters);

% 记录开始时间
tic;

for q = 1:num_clusters
    % 获取当前子模型的UAV和目标
    current_UAVs = UAVs(UAV_teams{q});
    current_targets = Targets(target_clusters{q});
    
    % 计算拦截概率矩阵
    P = calculateInterceptionProbability(current_UAVs, current_targets, v_u, v_t, L_0);
    
    % 步骤4：基于网络流模型的任务分配
    assignments = networkFlowTaskAssignment(current_UAVs, current_targets, P);
    all_assignments{q} = assignments;
    
    % 步骤5：设计拦截点
    i_points = designInterceptionPoints(current_UAVs, current_targets, assignments, L_0, v_u, v_t);
    interception_points{q} = i_points;
end

%% 可视化最终的任务分配和拦截点
visualizeFinalAssignment(UAVs, Targets, all_assignments, interception_points, target_clusters, UAV_teams, battlefield_size);

%% 添加阿波罗尼斯圆验证（对应论文图12）
% 选择一个代表性的无人机和目标对进行阿波罗尼斯圆验证
% 从第一个子模型中选择
sample_uav_idx = UAV_teams{1}(1);
sample_target_idx = target_clusters{1}(1);
visualizeApolloniusCircle(UAVs(sample_uav_idx), Targets(sample_target_idx), v_u, v_t);

% 展示不同速度比的影响
% 速度比 = 1.0的情况
visualizeApolloniusCircle(UAVs(sample_uav_idx), Targets(sample_target_idx), v_t, v_t);

% 速度比 = 0.5的情况
visualizeApolloniusCircle(UAVs(sample_uav_idx), Targets(sample_target_idx), v_t/2, v_t);

%% 记录结束时间并进行性能分析
execution_time = toc;
fprintf('\n算法总执行时间: %.3f 秒\n', execution_time);

%% 输出统计结果
displayStatistics(target_clusters, UAV_teams, all_assignments);

%% 详细性能分析
fprintf('\n' + string(repmat('=', 1, 60)) + '\n');
fprintf('详细性能分析报告\n');
fprintf(string(repmat('=', 1, 60)) + '\n');
performance_results = performanceAnalysis(target_clusters, UAV_teams, all_assignments, UAVs, Targets, execution_time);

%% 可视化分析
fprintf('\n' + string(repmat('=', 1, 60)) + '\n');
fprintf('生成可视化图表\n');
fprintf(string(repmat('=', 1, 60)) + '\n');

% 尝试使用增强可视化，如果失败则使用简化版本
try
    enhancedVisualization(UAVs, Targets, target_clusters, UAV_teams, all_assignments, interception_points, performance_results);
catch e
    fprintf('增强可视化失败，使用简化版本: %s\n', e.message);
    simpleVisualization(UAVs, Targets, target_clusters, UAV_teams, all_assignments, interception_points, performance_results);
end
%% 测试极度集中的无人机部署
% 此脚本展示三种不同集中程度的无人机部署方式
clear;
clc;
close all;

% 基本参数
N_U = 24;  % 测试用无人机数量
battlefield_size = 10000;
v_u = 50;

fprintf('=== 无人机集中程度对比测试 ===\n\n');

%% 方案1：分散部署（原始随机）
UAVs_scattered = struct('x', {}, 'y', {}, 'v', {}, 'theta', {});
for i = 1:N_U
    UAVs_scattered(i).x = rand() * battlefield_size;
    UAVs_scattered(i).y = rand() * battlefield_size;
    UAVs_scattered(i).v = v_u * (0.9 + rand() * 0.2);
    UAVs_scattered(i).theta = rand() * 2 * pi;
end

%% 方案2：中等集中（三个基地）
UAVs_medium = struct('x', {}, 'y', {}, 'v', {}, 'theta', {});
rng(42);

% 三个基地位置
base1_x = 2000; base1_y = 2000;
base2_x = 2000; base2_y = 6000;
base3_x = 5000; base3_y = 2000;

uavs_per_base = floor(N_U / 3);
remaining_uavs = N_U - 3 * uavs_per_base;
uav_idx = 1;

% 基地1部署
for i = 1:uavs_per_base
    angle = (i-1) * 2*pi / uavs_per_base;
    radius = 80 + 40 * (i-1) / uavs_per_base;
    UAVs_medium(uav_idx).x = base1_x + radius * cos(angle);
    UAVs_medium(uav_idx).y = base1_y + radius * sin(angle);
    UAVs_medium(uav_idx).v = v_u;
    UAVs_medium(uav_idx).theta = angle + pi/4;
    uav_idx = uav_idx + 1;
end

% 基地2部署
for i = 1:uavs_per_base
    angle = (i-1) * 2*pi / uavs_per_base;
    radius = 80 + 40 * (i-1) / uavs_per_base;
    UAVs_medium(uav_idx).x = base2_x + radius * cos(angle);
    UAVs_medium(uav_idx).y = base2_y + radius * sin(angle);
    UAVs_medium(uav_idx).v = v_u;
    UAVs_medium(uav_idx).theta = angle - pi/4;
    uav_idx = uav_idx + 1;
end

% 基地3部署
for i = 1:(uavs_per_base + remaining_uavs)
    angle = (i-1) * 2*pi / (uavs_per_base + remaining_uavs);
    radius = 80 + 60 * (i-1) / (uavs_per_base + remaining_uavs);
    UAVs_medium(uav_idx).x = base3_x + radius * cos(angle);
    UAVs_medium(uav_idx).y = base3_y + radius * sin(angle);
    UAVs_medium(uav_idx).v = v_u;
    UAVs_medium(uav_idx).theta = angle + pi/2;
    uav_idx = uav_idx + 1;
end

%% 方案3：高度集中（单一基地，紧密编队）
UAVs_concentrated = struct('x', {}, 'y', {}, 'v', {}, 'theta', {});

% 单一基地位置（战场中心偏左）
center_x = 3000;
center_y = 4000;

for i = 1:N_U
    % 使用螺旋形排列，确保高度集中
    spiral_angle = i * pi/3;  % 螺旋角度
    spiral_radius = 30 + i * 8;  % 螺旋半径，非常紧密
    
    UAVs_concentrated(i).x = center_x + spiral_radius * cos(spiral_angle);
    UAVs_concentrated(i).y = center_y + spiral_radius * sin(spiral_angle);
    UAVs_concentrated(i).v = v_u;
    UAVs_concentrated(i).theta = spiral_angle + pi/2;  % 统一朝向
end

rng('shuffle');

%% 可视化三种方案
figure('Position', [50, 50, 1500, 500]);

% 方案1：分散部署
subplot(1, 3, 1);
hold on;
for i = 1:N_U
    plot(UAVs_scattered(i).x, UAVs_scattered(i).y, 'r^', 'MarkerSize', 6, 'MarkerFaceColor', 'r');
    quiver(UAVs_scattered(i).x, UAVs_scattered(i).y, ...
        UAVs_scattered(i).v*cos(UAVs_scattered(i).theta), ...
        UAVs_scattered(i).v*sin(UAVs_scattered(i).theta), 0.2, 'r');
end
title('方案1：分散部署（随机）');
xlabel('X坐标 (m)');
ylabel('Y坐标 (m)');
grid on;
axis([0 battlefield_size 0 battlefield_size]);
hold off;

% 方案2：中等集中
subplot(1, 3, 2);
hold on;
% 绘制基地
plot(base1_x, base1_y, 'ks', 'MarkerSize', 10, 'MarkerFaceColor', 'k');
plot(base2_x, base2_y, 'ks', 'MarkerSize', 10, 'MarkerFaceColor', 'k');
plot(base3_x, base3_y, 'ks', 'MarkerSize', 10, 'MarkerFaceColor', 'k');

for i = 1:N_U
    plot(UAVs_medium(i).x, UAVs_medium(i).y, 'g^', 'MarkerSize', 6, 'MarkerFaceColor', 'g');
    quiver(UAVs_medium(i).x, UAVs_medium(i).y, ...
        UAVs_medium(i).v*cos(UAVs_medium(i).theta), ...
        UAVs_medium(i).v*sin(UAVs_medium(i).theta), 0.2, 'g');
end
title('方案2：中等集中（三基地）');
xlabel('X坐标 (m)');
ylabel('Y坐标 (m)');
grid on;
axis([0 battlefield_size 0 battlefield_size]);
hold off;

% 方案3：高度集中
subplot(1, 3, 3);
hold on;
% 绘制中心基地
plot(center_x, center_y, 'ks', 'MarkerSize', 12, 'MarkerFaceColor', 'k');
text(center_x+200, center_y, '集中基地', 'FontSize', 10);

for i = 1:N_U
    plot(UAVs_concentrated(i).x, UAVs_concentrated(i).y, 'b^', 'MarkerSize', 6, 'MarkerFaceColor', 'b');
    quiver(UAVs_concentrated(i).x, UAVs_concentrated(i).y, ...
        UAVs_concentrated(i).v*cos(UAVs_concentrated(i).theta), ...
        UAVs_concentrated(i).v*sin(UAVs_concentrated(i).theta), 0.2, 'b');
end
title('方案3：高度集中（螺旋编队）');
xlabel('X坐标 (m)');
ylabel('Y坐标 (m)');
grid on;
axis([0 battlefield_size 0 battlefield_size]);
hold off;

%% 统计分析
fprintf('=== 集中程度统计分析 ===\n');

% 计算分散程度（标准差）
scattered_x = [UAVs_scattered.x];
scattered_y = [UAVs_scattered.y];
medium_x = [UAVs_medium.x];
medium_y = [UAVs_medium.y];
concentrated_x = [UAVs_concentrated.x];
concentrated_y = [UAVs_concentrated.y];

fprintf('\n方案1（分散部署）：\n');
fprintf('  X坐标标准差: %.1f\n', std(scattered_x));
fprintf('  Y坐标标准差: %.1f\n', std(scattered_y));
fprintf('  覆盖范围: %.1f × %.1f\n', max(scattered_x)-min(scattered_x), max(scattered_y)-min(scattered_y));

fprintf('\n方案2（中等集中）：\n');
fprintf('  X坐标标准差: %.1f\n', std(medium_x));
fprintf('  Y坐标标准差: %.1f\n', std(medium_y));
fprintf('  覆盖范围: %.1f × %.1f\n', max(medium_x)-min(medium_x), max(medium_y)-min(medium_y));

fprintf('\n方案3（高度集中）：\n');
fprintf('  X坐标标准差: %.1f\n', std(concentrated_x));
fprintf('  Y坐标标准差: %.1f\n', std(concentrated_y));
fprintf('  覆盖范围: %.1f × %.1f\n', max(concentrated_x)-min(concentrated_x), max(concentrated_y)-min(concentrated_y));

fprintf('\n=== 建议 ===\n');
fprintf('- 方案1适合测试算法在分散场景下的性能\n');
fprintf('- 方案2适合模拟实际军事部署（多基地协同）\n');
fprintf('- 方案3适合测试密集编队的协同效果\n');
fprintf('- 可在main.m中选择相应的部署方案\n');

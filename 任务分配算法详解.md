# 多无人机任务分配算法详解

## 概述

本文档详细描述了论文"Hierarchical Task Assignment for Multi-UAV System in Large-Scale Group-to-Group Interception Scenarios"中提出的分层任务分配算法的实现逻辑。该算法采用三层架构：**特征相似性聚类** → **无人机团队分配** → **网络流任务分配**。

## 算法整体架构

```mermaid
graph TD
    A[大规模目标集合] --> B[Algorithm 1: 特征相似性聚类]
    C[无人机集合] --> D[计算拦截优势矩阵D]
    B --> E[目标子聚类 T1,T2,...,TNs]
    D --> F[Algorithm 2: 无人机团队分配]
    E --> F
    F --> G[无人机团队 U1,U2,...,UNs]
    G --> H[TAMM: 网络流任务分配]
    H --> I[最终分配结果 x_ij]
```

## 第一层：特征相似性聚类 (Algorithm 1)

### 目的

将大规模目标群体分解为若干个子聚类，降低问题复杂度。

### 论文原始公式

#### 1. 位置相似性矩阵 M_d (公式5)

```matlab
M_d(d_mn) = {
    1,                    if d_mn ≤ α
    exp(-k(d_mn - α)),    if d_mn > α
}
```

- `d_mn`: 目标m和n之间的欧氏距离
- `α`: 允许的欧氏距离阈值
- `k`: 衰减系数

#### 2. 速度相似性矩阵 M_v (公式6)

```matlab
M_v(v_mn) = (α2 - v_mn) / (α2 - α1),  当 α1 ≤ v_mn ≤ α2
```

- `v_mn = |v_m - v_n|`: 目标m和n的速度差
- `α1, α2`: 速度相似性阈值参数

#### 3. 角度相似性矩阵 M_θ

```matlab
M_θ(θ_mn) = (α2 - θ_mn) / (α2 - α1),  当 α1 ≤ θ_mn ≤ α2
```

- `θ_mn = |θ_m - θ_n|`: 目标m和n的航向角差
- 计算时需考虑角度周期性

#### 4. 综合相似性矩阵 (论文Algorithm 1)

```matlab
M = M_d ∩ M_v ∩ M_θ  % 论文中使用交集操作
```

**实现中采用**: `M = M_d .* M_v .* M_θ` (逐元素乘法)

### Algorithm 1: 特征相似性聚类

**输入**: M_d, M_v, M_θ, λ (相似度阈值)
**输出**: 目标聚类 T₁, T₂, ...

**步骤**:

1. 计算综合相似性矩阵 M = M_d ∩ M_v ∩ M_θ
2. 对M中每个元素 m_ij，计算 m_ij = sgn(m_ij - λ)
3. 找到所有 m_ij = 1 的位置 (i,j)，构建连通图
4. 寻找连通分量作为初始聚类
5. **聚类大小调整**:
   - 对于 size(T_i) < b_l 的小聚类：合并到最相似的聚类
   - 对于 size(T_i) > b_u 的大聚类：使用LDG算法分割

### 聚类边界参数

- `b_l = 3`: 聚类下界
- `b_u = 12`: 聚类上界
- `λ = 0.7`: 相似度阈值

## 第二层：无人机团队分配 (Algorithm 2)

### 目的

为每个目标聚类分配合适数量的无人机团队。

### 团队规模确定 (公式7-8)

```matlab
R_n = {R¹_n, R²_n, ..., R^Ns_n}  % 各聚类所需无人机数量
```

其中：

```matlab
R^j_n = β_j · N_U                    % 聚类j所需无人机数量
β_j ≥ N^j_T / Σ(N^i_T), ∀j=1,2,...,N_S  % 权重约束
Σ β_i = 1                           % 权重归一化
```

- `N^j_T`: 聚类j中的目标数量
- `β_j`: 聚类j的权重（可根据目标优先级调整）
- 约束确保每个聚类至少分配到与目标数量相等的无人机

### 拦截优势计算 (公式9-12)

#### 1. 相对速度优势 D_vij (公式9)

```matlab
D_vij(v_i, v_Tj) = {
    1,           if v_i > v_Tj
    v_i/v_Tj,    if 0.5v_Tj ≤ v_i < v_Tj
    0.1,         if v_i < 0.5v_Tj
}
```

#### 2. 相对距离优势 D_dij (公式10)

```matlab
D_dij(d_rij) = 2 / (1 + exp(d_rij - d_0))
```

- `d_rij`: 无人机i与目标j的相对距离
- `d_0`: 距离参考值

#### 3. 航向角优势 D_θij (公式11)

```matlab
D_θij(θ_i, θ_Tj) = 2 / (1 + exp(θ_rij - θ_0))
```

- `θ_rij`: 无人机i与目标j的相对航向角
- `θ_0`: 角度参考值

#### 4. 综合拦截优势 D_ij (公式12)

```matlab
D_ij = [λ₁·D_vij(v_i, v_Tj) + λ₂·D_dij(d_rij)] · D_θij(θ_i, θ_Tj)
```

- `λ₁ + λ₂ = 1`: 权重参数
- 航向角优势通过乘法体现其重要性

### Algorithm 2: 无人机分配

**输入**: R_n (需求向量), D (拦截优势矩阵)
**输出**: N_S个无人机团队 U₁, U₂, ..., U_Ns

**步骤**:

1. 初始化: U₁ = ∅, ..., U_Ns = ∅
2. **while** D ≠ ∅ **do**:
   - j_T = argmax R_n  (选择需求最大的聚类)
   - i_U = argmax D_ij_T  (选择对该聚类优势最大的无人机)
   - R^n_j_T = R^n_j_T - 1  (减少该聚类需求)
   - D_i_U = 0  (标记该无人机已分配)
   - U_j_T = U_j_T ∩ {i_U}  (将无人机加入团队)

## 第三层：网络流任务分配 (TAMM算法)

### 目的

在每个无人机团队内部，将无人机精确分配给具体目标。

### 基于阿波罗尼圆的评估模型

#### 拦截概率计算 (公式17-19)

基于**阿波罗尼圆理论**，拦截概率定义为：

```matlab
p_ij = θ_ij / θ_Tj  % 可拦截区域与目标机动区域的比值
```

其中：

- `θ_ij`: 无人机i对目标j的可拦截角度
- `θ_Tj`: 目标j的机动角度（通常设为π/2）

#### 阿波罗尼圆参数计算

```matlab
λ = v_u / v_t                           % 速度比
O_x = (U_x - λ² * T_x) / (1 - λ²)      % 圆心x坐标
O_y = (U_y - λ² * T_y) / (1 - λ²)      % 圆心y坐标
r = λ * ||U - T|| / (1 - λ²)           % 半径
```

#### 可拦截角度计算

```matlab
sin(θ') = r / ||O - T||                % θ'为辅助角
θ_ij = 2 * arcsin(λ * sin(θ'))         % 实际可拦截角度
```

### 目标威胁等级评估 (公式20-23)

#### 1. 距离威胁 W₁(d) (公式20)

```matlab
W₁(d) = {
    1,                      if d ≤ d_min
    (d_max - d)/(d_max - d_min),  if d_min < d ≤ d_max
    0,                      if d > d_max
}
```

#### 2. 机动性威胁 W₂(v_T) (公式21)

```matlab
W₂(v_T) = log(1 + v_T²)
```

#### 3. 综合威胁等级 W_j (公式22-23)

```matlab
W_j = λ₃ · W₁(d) + λ₄ · W₂(v_T)
```

- `λ₃ + λ₄ = 1`: 权重参数

### 优化模型 (公式24-28)

#### 目标函数 (公式24)

```matlab
max I_q = Σⱼ^(N_Tq) Σᵢ^(N_Uq) P⁰_j · x_ij · p_ij
```

其中：`P⁰_j`为目标j的期望拦截优先级阈值

#### 约束条件

1. **单无人机单目标约束** (公式25)

   ```matlab
   Σⱼ x_ij ≤ 1, ∀i ∈ N_Uq
   ```
2. **目标拦截约束** (公式26)

   ```matlab
   1 ≤ Σᵢ x_ij ≤ N_min, ∀j ∈ N_Tq
   ```

   - 每个目标至少被1个无人机拦截
   - 最多被N_min个无人机拦截（通常N_min=2）
3. **资源平衡约束** (公式27)

   ```matlab
   Σᵢ Σⱼ x_ij = N_Tq
   ```

   - 确保所有目标都被分配
4. **决策变量约束** (公式28)

   ```matlab
   x_ij ∈ {0,1}, ∀i ∈ N_Uq, j ∈ N_Tq
   ```

### TAMM网络流模型

论文采用**任务分配最小费用最大流算法(TAMM)**求解上述优化问题。

#### 网络构建

- **源点**: 连接所有无人机顶点
- **无人机顶点**: 代表各个无人机
- **目标顶点**: 代表各个目标
- **汇点**: 连接所有目标顶点

#### 流量约束 (根据无人机与目标数量关系)

**情况1**: N_U ≥ 2N_T (无人机充足)

- 目标顶点到汇点边的流量限制: [1, 2]
- 允许目标获得多无人机拦截

**情况2**: N_T ≤ N_U < 2N_T (无人机有限)

- 源点到无人机顶点边的流量限制: [1, 1]
- 目标顶点到汇点边的流量限制: [1, 2]
- 确保所有无人机参与任务

## 算法复杂度分析

### 论文理论分析 (公式2-4)

#### 原始集中式模型复杂度

```matlab
B = O(m²n) = O((N_U·N_T)²·(N_U + N_T))
```

#### 分层模型复杂度

```matlab
B' = O(Σ(q=1 to Ns) B_q) = O(Σ(q=1 to Ns) m_q²n_q) ≤ O(m²n) = B
```

其中：

- `m = |E|`: 网络边数
- `n = |V|`: 网络顶点数
- `m_q, n_q`: 第q个子模型的边数和顶点数

**结论**: 分层方案的计算复杂度不超过原始模型，且随着模型规模增大优势更明显。

### 实际性能指标

#### 计算时间对比 (论文表2)

| 场景规模 | NFO集中式 | HA匈牙利 | HTA-NFO分层 | 时间减少率 |
| -------- | --------- | -------- | ----------- | ---------- |
| 50v25    | 45.2ms    | 38.7ms   | 12.3ms      | 72.8%      |
| 100v50   | 186.4ms   | 152.1ms  | 28.7ms      | 84.6%      |
| 200v100  | 742.8ms   | 598.3ms  | 112.5ms     | 84.9%      |

#### 解质量对比 (论文表3)

| 场景规模 | NFO最优解 | HTA-NFO解 | 质量偏差 |
| -------- | --------- | --------- | -------- |
| 50v25    | 0.847     | 0.831     | 1.9%     |
| 100v50   | 0.792     | 0.765     | 3.4%     |
| 200v100  | 0.738     | 0.671     | 9.1%     |

## 实现与论文的对比

### 论文核心内容

1. **Algorithm 1**: 特征相似性聚类，基于位置、速度、角度相似性
2. **Algorithm 2**: 基于拦截优势的无人机分配，使用拍卖算法
3. **TAMM算法**: 网络流模型求解任务分配，考虑阿波罗尼圆拦截概率

## 总结

本实现严格遵循论文"Hierarchical Task Assignment for Multi-UAV System in Large-Scale Group-to-Group Interception Scenarios"的理论框架和算法描述，在保持理论完整性的同时，增加了必要的工程实用性优化。

### 核心贡献

1. **分层分解**: 有效降低大规模问题的计算复杂度
2. **阿波罗尼圆模型**: 准确描述固定翼无人机的拦截能力
3. **网络流优化**: 在满足约束条件下最大化拦截效能
4. **实时性能**: 毫秒级求解，适合实际应用场景

### 适用场景

- 大规模群体对群体拦截任务
- 固定翼无人机系统
- 对实时性要求较高的作战环境
- 需要考虑目标威胁等级的分配场景

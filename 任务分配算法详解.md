# 多无人机任务分配算法详解

## 概述

本文档详细描述了基于层次化任务分配的多无人机系统任务分配算法的实现逻辑。该算法采用三层架构：**特征相似性聚类** → **无人机团队分配** → **网络流任务分配**。

## 算法整体架构

```mermaid
graph TD
    A[目标集合] --> B[特征相似性聚类]
    C[无人机集合] --> D[计算拦截优势]
    B --> E[目标聚类]
    D --> F[无人机团队分配]
    E --> F
    F --> G[团队内任务分配]
    G --> H[最终分配结果]
```

## 第一层：特征相似性聚类

### 目的
将大规模目标群体分解为若干个子聚类，降低问题复杂度。

### 核心思想
基于目标的**位置相似性**、**速度相似性**和**角度相似性**进行聚类。

### 相似性计算

#### 1. 位置相似性矩阵 M_d
```matlab
M_d(i,j) = exp(-||p_i - p_j||^2 / α^2)
```
- `p_i, p_j`: 目标i和j的位置坐标
- `α`: 位置相似性参数（默认1000m）

#### 2. 速度相似性矩阵 M_v
```matlab
M_v(i,j) = exp(-|v_i - v_j|^2 / (2σ_v^2))
```
其中：
```matlab
σ_v = {
    α1_v,           if |v_i - v_j| ≤ α1_v
    α1_v + (α2_v - α1_v) * (|v_i - v_j| - α1_v)/(α2_v - α1_v), 
                    if α1_v < |v_i - v_j| ≤ α2_v
    α2_v,           if |v_i - v_j| > α2_v
}
```
- `α1_v = 5 m/s`, `α2_v = 10 m/s`（论文原始参数）

#### 3. 角度相似性矩阵 M_θ
```matlab
M_θ(i,j) = exp(-θ_diff^2 / (2σ_θ^2))
```
其中：
```matlab
θ_diff = min(|θ_i - θ_j|, 2π - |θ_i - θ_j|)  % 考虑角度周期性
σ_θ = 类似速度相似性的分段函数，α1_θ = 5°, α2_θ = 10°
```

#### 4. 综合相似性矩阵
```matlab
S = λ * M_d + (1-λ) * (M_v ⊙ M_θ)
```
- `λ = 0.7`: 位置权重参数
- `⊙`: 逐元素乘法

### 聚类过程

1. **初始化**: 每个目标作为独立聚类
2. **图合并**: 基于相似性阈值合并相似聚类
3. **图分割**: 将过大聚类（>b_u=12）分割
4. **小聚类处理**: 将过小聚类（<b_l=3）合并到最相似的聚类

## 第二层：无人机团队分配

### 目的
为每个目标聚类分配合适数量的无人机团队。

### 拦截优势计算

对每个无人机i和聚类q，计算拦截优势：
```matlab
D(i,q) = λ1 * D_pos(i,q) + λ2 * D_threat(i,q)
```

#### 位置优势 D_pos
```matlab
D_pos(i,q) = exp(-d_min(i,q)^2 / σ_pos^2)
```
- `d_min(i,q)`: 无人机i到聚类q中最近目标的距离

#### 威胁优势 D_threat
```matlab
D_threat(i,q) = Σ(威胁度_j) / |聚类q|
```
- 聚类内所有目标威胁度的平均值

### 团队规模确定

```matlab
R_n(q) = max(ceil(β_q * N_U), N_T_q)
```
- `β_q = N_T_q / Σ(N_T_j)`: 聚类q的目标比例
- `N_T_q`: 聚类q的目标数量
- 确保每个聚类至少有与目标数量相等的无人机

### 分配策略

采用**贪心拍卖算法**：
1. 按聚类需求顺序分配
2. 每次选择对当前聚类拦截优势最大的未分配无人机
3. 标记该无人机为已分配，继续下一个

## 第三层：网络流任务分配

### 目的
在每个无人机团队内部，将无人机精确分配给具体目标。

### 拦截概率计算

基于**阿波罗尼圆理论**计算无人机i拦截目标j的概率：

#### 1. 阿波罗尼圆参数
```matlab
λ = v_u / v_t                    % 速度比
O_x = (U_x - λ² * T_x) / (1 - λ²)  % 圆心x坐标
O_y = (U_y - λ² * T_y) / (1 - λ²)  % 圆心y坐标
r = λ * ||U - T|| / (1 - λ²)      % 半径
```

#### 2. 可拦截角度
```matlab
sin(θ') = r / ||O - T||
θ = 2 * arcsin(λ * sin(θ'))
```

#### 3. 拦截概率
```matlab
p_ij = min(1, θ / θ_T)
P_ij = P⁰_j * p_ij
```
- `θ_T = π/2`: 目标机动角度
- `P⁰_j = max(0.5, 威胁度_j)`: 期望拦截优先级

### 优化目标

**两阶段整数线性规划**：

#### 目标函数
```matlab
minimize: Σ Σ C_ij * x_ij
```
其中：`C_ij = -P_ij * 威胁权重_j`（负值转换为最小化问题）

#### 约束条件

1. **无人机约束**：每个无人机最多拦截一个目标
   ```matlab
   Σ_j x_ij ≤ 1, ∀i
   ```

2. **目标覆盖约束**：每个目标至少被一个无人机拦截
   ```matlab
   Σ_i x_ij ≥ 1, ∀j
   ```

3. **多机协同约束**：每个目标最多被两个无人机拦截
   ```matlab
   Σ_i x_ij ≤ 2, ∀j
   ```

4. **协同率约束**：至少80%目标获得多无人机拦截
   ```matlab
   Σ_j Σ_i x_ij ≥ N_T + 0.8 * N_T
   ```

### 威胁度权重机制

为确保高威胁目标优先获得多机协同：
```matlab
威胁权重_j = {
    1.5,  if 目标j属于前50%高威胁目标
    1.0,  otherwise
}
```

### 备用贪心算法

当整数线性规划无解时，采用两阶段贪心：

#### 阶段1：基础覆盖
- 按威胁度降序处理目标
- 为每个目标分配拦截概率最高的可用无人机

#### 阶段2：多机增强
- 为剩余无人机寻找最适合的高威胁目标
- 优先增强前几个最高威胁目标的拦截能力

## 算法特点

### 优势
1. **层次化分解**：有效降低大规模问题复杂度
2. **威胁导向**：高威胁目标优先获得资源
3. **协同保证**：确保80%目标获得多机拦截
4. **理论基础**：基于阿波罗尼圆的拦截概率计算
5. **鲁棒性**：整数规划+贪心备用方案

### 复杂度分析
- **聚类阶段**：O(N_T²)
- **团队分配**：O(N_U * N_C)
- **任务分配**：O(N_U * N_T) per cluster

### 性能指标
- **目标分配率**：通常达到100%
- **多机协同率**：目标80%，实际可达80-100%
- **计算时间**：中等规模问题(<100目标)约3-5秒

## 与论文的差异

### 论文描述
- 主要描述了聚类和拦截概率计算的理论基础
- 任务分配部分描述较为简略

### 实现增强
1. **威胁度权重机制**：确保高威胁目标优先级
2. **两阶段优化**：先保证覆盖，再优化协同
3. **动态约束调整**：无人机不足时自动降低协同率要求
4. **备用算法**：保证在任何情况下都能得到可行解

## 总结

该算法通过层次化分解和多目标优化，在保证任务完成率的同时，最大化了高威胁目标的多机协同拦截率。整个系统具有良好的可扩展性和实用性，适合大规模多无人机作战场景。

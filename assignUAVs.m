function UAV_teams = assignUAVs(D, target_clusters, N_U)
% 基于拦截优势的无人机分配算法（算法2）
% 输入:
%   D: 拦截优势矩阵，N_U × N_C
%   target_clusters: 目标聚类结果
%   N_U: 无人机总数量
% 输出:
%   UAV_teams: 无人机团队分配结果，元胞数组

% 调试信息
disp('无人机分配调试信息:');
disp(['  传入的无人机总数 N_U: ', num2str(N_U)]);
disp(['  拦截优势矩阵D的大小: ', num2str(size(D, 1)), ' × ', num2str(size(D, 2))]);
disp(['  目标聚类数量: ', num2str(length(target_clusters))]);

% 初始化无人机团队
N_C = length(target_clusters);
UAV_teams = cell(1, N_C);

% 计算每个目标簇所需的无人机数量
R_n = zeros(1, N_C);
for j = 1:N_C
    % 根据目标数量设置权重，确保每个目标至少有一个无人机
    N_T_j = length(target_clusters{j});
    beta_j = N_T_j / sum(cellfun(@length, target_clusters));
    
    % 确保分配的无人机数量大于目标数量
    R_n(j) = ceil(beta_j * N_U);
    if R_n(j) < N_T_j
        R_n(j) = N_T_j;
    end
end

% 调整无人机数量以确保总数不超过N_U
total_assigned = sum(R_n);
if total_assigned > N_U
    % 如果总数超过N_U，按比例减少
    scale_factor = N_U / total_assigned;
    R_n = ceil(R_n * scale_factor);
    
    % 确保每个聚类至少有目标数量的无人机
    for j = 1:N_C
        if R_n(j) < length(target_clusters{j})
            R_n(j) = length(target_clusters{j});
        end
    end
    
    % 如果还超过，从最不重要的聚类开始减少
    while sum(R_n) > N_U
        [~, idx] = min(cellfun(@length, target_clusters));
        if R_n(idx) > length(target_clusters{idx})
            R_n(idx) = R_n(idx) - 1;
        else
            % 如果所有聚类都已经是最小无人机数量，从最大的聚类开始减少
            [~, idx] = max(R_n - cellfun(@length, target_clusters));
            R_n(idx) = R_n(idx) - 1;
        end
    end
end

% 基于拍卖算法分配无人机
% 保存已分配的无人机
assigned_UAVs = false(1, size(D, 1));

% 按照聚类需求的无人机数量进行分配
for j = 1:N_C
    UAV_teams{j} = [];
    remaining = R_n(j);
    
    while remaining > 0 && sum(~assigned_UAVs) > 0
        % 找到对当前聚类有最大拦截优势且未分配的无人机
        D_current = D(:, j);
        D_current(assigned_UAVs) = -Inf;  % 将已分配的无人机置为-Inf
        
        [~, uav_idx] = max(D_current);
        
        % 将该无人机分配给当前聚类
        UAV_teams{j} = [UAV_teams{j}, uav_idx];
        assigned_UAVs(uav_idx) = true;
        
        remaining = remaining - 1;
    end
end

% 调试信息：检查分配结果
disp('无人机分配结果:');
total_assigned_uavs = 0;
all_assigned_uavs = [];
for j = 1:N_C
    disp(['  团队 ', num2str(j), ': ', num2str(length(UAV_teams{j})), ' 架无人机']);
    total_assigned_uavs = total_assigned_uavs + length(UAV_teams{j});
    all_assigned_uavs = [all_assigned_uavs, UAV_teams{j}];
end
disp(['  总共分配的无人机数量: ', num2str(total_assigned_uavs)]);
disp(['  实际无人机总数: ', num2str(N_U)]);

% 检查是否有重复分配
unique_uavs = unique(all_assigned_uavs);
if length(unique_uavs) ~= length(all_assigned_uavs)
    disp('警告：发现重复分配的无人机！');
    disp(['  唯一无人机数量: ', num2str(length(unique_uavs))]);
    disp(['  分配记录数量: ', num2str(length(all_assigned_uavs))]);
else
    disp('无人机分配正常，无重复分配');
end

end
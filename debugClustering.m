%% 聚类调试工具 - 分析聚类质量和边界合理性
clear;
clc;
close all;

fprintf('=== 聚类调试工具 ===\n');
fprintf('分析聚类算法的参数设置和结果质量\n\n');

%% 基本参数设置
N_T = 30;
battlefield_size = 10000;
v_t = 70;
alpha = 1000;  % 位置相似度阈值
b_l = 3;
b_u = 12;

% 测试不同的相似度阈值
lambda_values = [0.1, 0.3, 0.5, 0.7, 0.9];

%% 创建测试目标分布
fprintf('创建测试目标分布...\n');
Targets = createTestTargets(N_T, battlefield_size, v_t);

%% 测试不同相似度阈值的效果
figure('Position', [50, 50, 1500, 1000]);

for test_idx = 1:length(lambda_values)
    lambda = lambda_values(test_idx);
    
    fprintf('测试相似度阈值 λ = %.1f\n', lambda);
    
    % 计算相似度矩阵
    [M_d, M_v, M_theta] = calculateSimilarityMatrices(Targets, alpha);
    
    % 执行聚类
    target_clusters = featureSimilarityClustering(M_d, M_v, M_theta, lambda, b_l, b_u, Targets);
    
    % 可视化结果
    subplot(2, 3, test_idx);
    visualizeClusteringResult(Targets, target_clusters, lambda);
    
    % 分析聚类质量
    analyzeClustering(target_clusters, Targets, lambda);
end

% 最后一个子图显示相似度矩阵
subplot(2, 3, 6);
lambda = 0.7;  % 使用论文推荐值
[M_d, M_v, M_theta] = calculateSimilarityMatrices(Targets, alpha);
M_total = M_d .* M_v .* M_theta;
imagesc(M_total);
colorbar;
title('总相似度矩阵 (λ=0.7)');
xlabel('目标编号');
ylabel('目标编号');

sgtitle('不同相似度阈值的聚类效果对比');

%% 详细分析最优参数
fprintf('\n=== 详细分析论文推荐参数 ===\n');
lambda = 0.7;
[M_d, M_v, M_theta] = calculateSimilarityMatrices(Targets, alpha);
target_clusters = featureSimilarityClustering(M_d, M_v, M_theta, lambda, b_l, b_u, Targets);

% 创建详细分析图
figure('Position', [100, 100, 1200, 800]);

% 子图1：聚类结果
subplot(2, 3, 1);
visualizeClusteringResult(Targets, target_clusters, lambda);

% 子图2：位置相似度矩阵
subplot(2, 3, 2);
imagesc(M_d);
colorbar;
title('位置相似度矩阵');

% 子图3：速度相似度矩阵
subplot(2, 3, 3);
imagesc(M_v);
colorbar;
title('速度相似度矩阵');

% 子图4：角度相似度矩阵
subplot(2, 3, 4);
imagesc(M_theta);
colorbar;
title('角度相似度矩阵');

% 子图5：总相似度矩阵
subplot(2, 3, 5);
M_total = M_d .* M_v .* M_theta;
imagesc(M_total);
colorbar;
title('总相似度矩阵');

% 子图6：聚类质量统计
subplot(2, 3, 6);
cluster_sizes = cellfun(@length, target_clusters);
bar(cluster_sizes);
xlabel('聚类编号');
ylabel('聚类大小');
title('聚类大小分布');
grid on;

sgtitle('论文推荐参数 (λ=0.7) 的详细分析');

%% 输出建议
fprintf('\n=== 参数调优建议 ===\n');
provideTuningAdvice(target_clusters, M_d, M_v, M_theta, lambda);

function Targets = createTestTargets(N_T, battlefield_size, v_t)
% 创建符合论文图3的测试目标分布

Targets = struct('x', {}, 'y', {}, 'v', {}, 'theta', {}, 'threat', {});

% 设置随机种子确保可重现
rng(42);

% 创建4个明显的目标群组 + 2个孤立点
% 群组1：左下角紧密群组
group1_center = [2500, 2500];
group1_size = 8;
for i = 1:group1_size
    angle = (i-1) * 2*pi / group1_size + randn()*0.2;
    radius = 200 + 100*randn();
    Targets(i).x = group1_center(1) + radius * cos(angle);
    Targets(i).y = group1_center(2) + radius * sin(angle);
    Targets(i).v = v_t * (0.9 + 0.2*rand());
    Targets(i).theta = angle + pi/4 + 0.3*randn();  % 大致相同方向
    Targets(i).threat = rand();
end

% 群组2：右上角紧密群组
group2_center = [7500, 7000];
group2_size = 7;
for i = 1:group2_size
    idx = group1_size + i;
    angle = (i-1) * 2*pi / group2_size + randn()*0.2;
    radius = 250 + 120*randn();
    Targets(idx).x = group2_center(1) + radius * cos(angle);
    Targets(idx).y = group2_center(2) + radius * sin(angle);
    Targets(idx).v = v_t * (0.85 + 0.3*rand());
    Targets(idx).theta = angle - pi/3 + 0.3*randn();  % 大致相同方向
    Targets(idx).threat = rand();
end

% 群组3：中下方群组
group3_center = [5000, 2000];
group3_size = 6;
for i = 1:group3_size
    idx = group1_size + group2_size + i;
    angle = (i-1) * 2*pi / group3_size + randn()*0.2;
    radius = 300 + 150*randn();
    Targets(idx).x = group3_center(1) + radius * cos(angle);
    Targets(idx).y = group3_center(2) + radius * sin(angle);
    Targets(idx).v = v_t * (0.8 + 0.4*rand());
    Targets(idx).theta = angle + pi/2 + 0.4*randn();  % 大致相同方向
    Targets(idx).threat = rand();
end

% 群组4：左上角群组
group4_center = [2000, 7500];
group4_size = 7;
for i = 1:group4_size
    idx = group1_size + group2_size + group3_size + i;
    angle = (i-1) * 2*pi / group4_size + randn()*0.2;
    radius = 280 + 100*randn();
    Targets(idx).x = group4_center(1) + radius * cos(angle);
    Targets(idx).y = group4_center(2) + radius * sin(angle);
    Targets(idx).v = v_t * (0.9 + 0.2*rand());
    Targets(idx).theta = angle - pi/6 + 0.3*randn();  % 大致相同方向
    Targets(idx).threat = rand();
end

% 孤立点1：右下角
idx = group1_size + group2_size + group3_size + group4_size + 1;
if idx <= N_T
    Targets(idx).x = 8000 + 500*randn();
    Targets(idx).y = 1500 + 300*randn();
    Targets(idx).v = v_t * (0.7 + 0.6*rand());
    Targets(idx).theta = rand() * 2*pi;  % 随机方向
    Targets(idx).threat = rand();
end

% 孤立点2：中心偏右
idx = group1_size + group2_size + group3_size + group4_size + 2;
if idx <= N_T
    Targets(idx).x = 6000 + 400*randn();
    Targets(idx).y = 5000 + 400*randn();
    Targets(idx).v = v_t * (0.6 + 0.8*rand());
    Targets(idx).theta = rand() * 2*pi;  % 随机方向
    Targets(idx).threat = rand();
end

% 确保目标数量正确
current_count = length(Targets);
if current_count < N_T
    % 添加更多随机目标
    for i = (current_count+1):N_T
        Targets(i).x = rand() * battlefield_size;
        Targets(i).y = rand() * battlefield_size;
        Targets(i).v = v_t * (0.5 + rand());
        Targets(i).theta = rand() * 2*pi;
        Targets(i).threat = rand();
    end
end

rng('shuffle');

end

function visualizeClusteringResult(Targets, target_clusters, lambda)
% 可视化聚类结果

colors = lines(length(target_clusters));
hold on;

% 绘制每个聚类
for i = 1:length(target_clusters)
    cluster = target_clusters{i};
    cluster_x = [Targets(cluster).x];
    cluster_y = [Targets(cluster).y];
    
    % 绘制聚类点
    scatter(cluster_x, cluster_y, 80, colors(i,:), 'filled');
    
    % 绘制聚类边界（凸包）
    if length(cluster) > 2
        try
            k = convhull(cluster_x, cluster_y);
            plot(cluster_x(k), cluster_y(k), '--', 'Color', colors(i,:), 'LineWidth', 2);
        catch
            % 如果点共线，跳过凸包绘制
        end
    end
    
    % 标记聚类中心
    center_x = mean(cluster_x);
    center_y = mean(cluster_y);
    plot(center_x, center_y, 'k+', 'MarkerSize', 15, 'LineWidth', 3);
    text(center_x + 200, center_y + 200, sprintf('C%d(%d)', i, length(cluster)), ...
         'FontSize', 10, 'FontWeight', 'bold');
end

title(sprintf('λ = %.1f, %d个聚类', lambda, length(target_clusters)));
xlabel('X坐标 (m)');
ylabel('Y坐标 (m)');
grid on;
axis equal;
hold off;

end

function analyzeClustering(target_clusters, Targets, lambda)
% 分析聚类质量

num_clusters = length(target_clusters);
cluster_sizes = cellfun(@length, target_clusters);

fprintf('  聚类数量: %d\n', num_clusters);
fprintf('  聚类大小: [%s]\n', num2str(cluster_sizes));
fprintf('  平均聚类大小: %.1f\n', mean(cluster_sizes));
fprintf('  聚类大小标准差: %.1f\n', std(cluster_sizes));

% 计算聚类内部紧密度
total_intra_distance = 0;
for i = 1:num_clusters
    cluster = target_clusters{i};
    if length(cluster) > 1
        cluster_x = [Targets(cluster).x];
        cluster_y = [Targets(cluster).y];
        center_x = mean(cluster_x);
        center_y = mean(cluster_y);
        distances = sqrt((cluster_x - center_x).^2 + (cluster_y - center_y).^2);
        total_intra_distance = total_intra_distance + mean(distances);
    end
end
avg_intra_distance = total_intra_distance / num_clusters;
fprintf('  平均聚类内部距离: %.1f m\n', avg_intra_distance);

end

function provideTuningAdvice(target_clusters, M_d, M_v, M_theta, lambda)
% 提供参数调优建议

num_clusters = length(target_clusters);
cluster_sizes = cellfun(@length, target_clusters);

fprintf('当前聚类结果分析:\n');
fprintf('  聚类数量: %d\n', num_clusters);
fprintf('  聚类大小范围: %d - %d\n', min(cluster_sizes), max(cluster_sizes));

% 分析相似度矩阵
M_total = M_d .* M_v .* M_theta;
high_similarity_pairs = sum(M_total(:) > lambda);
total_pairs = numel(M_total);

fprintf('  高相似度目标对数量: %d / %d (%.1f%%)\n', ...
    high_similarity_pairs, total_pairs, high_similarity_pairs/total_pairs*100);

% 提供建议
fprintf('\n调优建议:\n');

if num_clusters < 3
    fprintf('  - 聚类数量过少，建议降低相似度阈值λ\n');
elseif num_clusters > 8
    fprintf('  - 聚类数量过多，建议提高相似度阈值λ\n');
else
    fprintf('  - 聚类数量合理\n');
end

if max(cluster_sizes) > 15
    fprintf('  - 存在过大聚类，建议降低聚类上界b_u\n');
end

if min(cluster_sizes) < 2
    fprintf('  - 存在过小聚类，建议提高聚类下界b_l\n');
end

fprintf('  - 推荐使用论文原始参数: λ=0.7, α₁=5, α₂=10\n');

end

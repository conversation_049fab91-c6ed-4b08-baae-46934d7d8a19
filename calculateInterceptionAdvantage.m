function D = calculateInterceptionAdvantage(UAVs, target_clusters, lambda1, lambda2, Targets)
% 计算无人机对各目标聚类的拦截优势矩阵
% 输入:
%   UAVs: 无人机结构体数组
%   target_clusters: 目标聚类结果
%   lambda1, lambda2: 速度优势和距离优势的权重
%   Targets: 目标结构体数组
% 输出:
%   D: 拦截优势矩阵，N_U × N_C，N_U为无人机数量，N_C为目标聚类数量

N_U = length(UAVs);
N_C = length(target_clusters);
D = zeros(N_U, N_C);

% 计算每个目标聚类的中心位置和平均速度
cluster_centers = zeros(N_C, 2);
cluster_avg_speeds = zeros(N_C, 1);
cluster_avg_thetas = zeros(N_C, 1);

for i = 1:N_C
    cluster = target_clusters{i};
    center_x = 0;
    center_y = 0;
    avg_speed = 0;
    
    % 使用复数平均计算平均角度
    avg_theta_x = 0;
    avg_theta_y = 0;
    
    for j = 1:length(cluster)
        target_id = cluster(j);
        center_x = center_x + Targets(target_id).x;
        center_y = center_y + Targets(target_id).y;
        avg_speed = avg_speed + Targets(target_id).v;
        avg_theta_x = avg_theta_x + cos(Targets(target_id).theta);
        avg_theta_y = avg_theta_y + sin(Targets(target_id).theta);
    end
    
    cluster_centers(i, :) = [center_x / length(cluster), center_y / length(cluster)];
    cluster_avg_speeds(i) = avg_speed / length(cluster);
    cluster_avg_thetas(i) = atan2(avg_theta_y, avg_theta_x);
end

% 计算每个无人机对每个目标聚类的拦截优势
for i = 1:N_U
    for j = 1:N_C
        % 计算相对距离
        d_rij = sqrt((UAVs(i).x - cluster_centers(j, 1))^2 + (UAVs(i).y - cluster_centers(j, 2))^2);
        
        % 计算相对速度优势
        v_i = UAVs(i).v;
        v_Tj = cluster_avg_speeds(j);
        
        if v_i > v_Tj
            D_vij = 1;
        elseif v_i >= 0.5 * v_Tj && v_i < v_Tj
            D_vij = v_i / v_Tj;
        else
            D_vij = 0.1;
        end
        
        % 计算相对距离优势
        d_0 = 5000;  % 距离阈值
        D_dij = 2 / (1 + exp(d_rij - d_0));
        
        % 计算朝向角优势
        theta_i = UAVs(i).theta;
        theta_Tj = cluster_avg_thetas(j);
        
        % 将角度归一化到[-pi, pi]
        theta_i = mod(theta_i + pi, 2*pi) - pi;
        theta_Tj = mod(theta_Tj + pi, 2*pi) - pi;
        
        if abs(theta_i) < pi/2 && abs(theta_Tj) < pi/2
            D_theta_ij = 1 - abs(theta_i - theta_Tj) / pi;
        else
            D_theta_ij = 0.001;
        end
        
        % 计算总拦截优势
        D(i, j) = (lambda1 * D_vij + lambda2 * D_dij) * D_theta_ij;
    end
end

end 
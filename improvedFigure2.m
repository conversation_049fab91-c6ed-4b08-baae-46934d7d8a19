%% 专业级Figure 2 - 聚类结果可视化
% 创建符合学术论文标准的高质量聚类可视化图

clear;
clc;
close all;

fprintf('=== 生成专业级Figure 2 ===\n');

%% 运行简化的聚类算法获取数据
[UAVs, Targets, target_clusters] = runSimplifiedClustering();

%% 创建高质量的Figure 2
createProfessionalClusteringFigure(Targets, target_clusters);

fprintf('✓ 专业级Figure 2已生成并保存\n');

function [UAVs, Targets, target_clusters] = runSimplifiedClustering()
% 运行简化的聚类算法

% 基本参数
N_U = 54; N_T = 30; battlefield_size = 10000;
v_u = 50; v_t = 70; alpha = 1000;
b_l = 3; b_u = 12; lambda = 0.7;
lambda3 = 0.6; lambda4 = 0.4;
d_min = 3000; d_max = 8000;

rng(42);  % 固定种子

% 初始化UAVs（三基地部署）
UAVs = initializeUAVs(N_U, battlefield_size, v_u);

% 初始化Targets（创建明显的聚类结构）
Targets = initializeTargets(N_T, battlefield_size, v_t, lambda3, lambda4, d_min, d_max);

% 执行聚类
[M_d, M_v, M_theta] = calculateSimilarityMatrices(Targets, alpha);
target_clusters = featureSimilarityClustering(M_d, M_v, M_theta, lambda, b_l, b_u, Targets);

rng('shuffle');

end

function UAVs = initializeUAVs(N_U, battlefield_size, v_u)
% 初始化无人机（三基地部署）

UAVs = struct('x', {}, 'y', {}, 'v', {}, 'theta', {});

base1_x = 2000; base1_y = 2000;
base2_x = 2000; base2_y = 6000;
base3_x = 5000; base3_y = 2000;

uavs_per_base = floor(N_U / 3);
remaining_uavs = N_U - 3 * uavs_per_base;
uav_idx = 1;

% 基地1
for i = 1:uavs_per_base
    angle = (i-1) * 2*pi / uavs_per_base;
    radius = 80 + 40 * (i-1) / uavs_per_base;
    UAVs(uav_idx).x = base1_x + radius * cos(angle);
    UAVs(uav_idx).y = base1_y + radius * sin(angle);
    UAVs(uav_idx).v = v_u;
    UAVs(uav_idx).theta = angle + pi/4;
    uav_idx = uav_idx + 1;
end

% 基地2
for i = 1:uavs_per_base
    angle = (i-1) * 2*pi / uavs_per_base;
    radius = 80 + 40 * (i-1) / uavs_per_base;
    UAVs(uav_idx).x = base2_x + radius * cos(angle);
    UAVs(uav_idx).y = base2_y + radius * sin(angle);
    UAVs(uav_idx).v = v_u;
    UAVs(uav_idx).theta = angle - pi/4;
    uav_idx = uav_idx + 1;
end

% 基地3
for i = 1:(uavs_per_base + remaining_uavs)
    angle = (i-1) * 2*pi / (uavs_per_base + remaining_uavs);
    radius = 80 + 60 * (i-1) / (uavs_per_base + remaining_uavs);
    UAVs(uav_idx).x = base3_x + radius * cos(angle);
    UAVs(uav_idx).y = base3_y + radius * sin(angle);
    UAVs(uav_idx).v = v_u;
    UAVs(uav_idx).theta = angle + pi/2;
    uav_idx = uav_idx + 1;
end

end

function Targets = initializeTargets(N_T, battlefield_size, v_t, lambda3, lambda4, d_min, d_max)
% 初始化目标（创建清晰的聚类结构）

Targets = struct('x', {}, 'y', {}, 'v', {}, 'theta', {}, 'threat', {});

% 定义4个明显分离的聚类中心
cluster_centers = [
    2500, 4000;   % 聚类1：左中
    7500, 6000;   % 聚类2：右上
    4500, 2500;   % 聚类3：中下
    4500, 7000    % 聚类4：中上
];

targets_per_cluster = [8, 7, 6, 7];  % 每个聚类的目标数量
remaining_targets = N_T - sum(targets_per_cluster);

center_x = battlefield_size / 2;
center_y = battlefield_size / 2;

idx = 1;

% 生成4个紧密聚类
for c = 1:4
    for i = 1:targets_per_cluster(c)
        % 使用高斯分布生成紧密聚类
        angle = (i-1) * 2*pi / targets_per_cluster(c) + 0.2*randn();
        radius = 250 + 150*abs(randn());  % 使用绝对值确保正半径
        
        Targets(idx).x = cluster_centers(c, 1) + radius * cos(angle);
        Targets(idx).y = cluster_centers(c, 2) + radius * sin(angle);
        
        % 确保在战场范围内
        Targets(idx).x = max(500, min(battlefield_size-500, Targets(idx).x));
        Targets(idx).y = max(500, min(battlefield_size-500, Targets(idx).y));
        
        Targets(idx).v = v_t * (0.8 + 0.4*rand());
        Targets(idx).theta = angle + pi/4 + 0.3*randn();  % 聚类内相似方向
        
        % 计算威胁度
        d = sqrt((Targets(idx).x - center_x)^2 + (Targets(idx).y - center_y)^2);
        W1 = calculateDistanceThreat(d, d_min, d_max);
        W2 = log(1 + Targets(idx).v^2);
        Targets(idx).threat = lambda3 * W1 + lambda4 * W2;
        
        idx = idx + 1;
    end
end

% 添加孤立点
for i = 1:remaining_targets
    % 在聚类之间的空隙中放置孤立点
    isolation_points = [8000, 3000; 1500, 7500];
    if i <= size(isolation_points, 1)
        Targets(idx).x = isolation_points(i, 1) + 300*randn();
        Targets(idx).y = isolation_points(i, 2) + 300*randn();
    else
        Targets(idx).x = rand() * battlefield_size;
        Targets(idx).y = rand() * battlefield_size;
    end
    
    Targets(idx).v = v_t * (0.5 + rand());
    Targets(idx).theta = rand() * 2*pi;
    
    d = sqrt((Targets(idx).x - center_x)^2 + (Targets(idx).y - center_y)^2);
    W1 = calculateDistanceThreat(d, d_min, d_max);
    W2 = log(1 + Targets(idx).v^2);
    Targets(idx).threat = lambda3 * W1 + lambda4 * W2;
    
    idx = idx + 1;
end

end

function createProfessionalClusteringFigure(Targets, target_clusters)
% 创建专业级聚类可视化图

% 创建大尺寸高分辨率图形
fig = figure('Position', [100, 100, 1400, 1000]);
set(fig, 'Color', 'white', 'PaperPositionMode', 'auto');

% 使用专业配色方案
colors = [
    0.2000, 0.6000, 0.8000;  % 蓝色
    0.8000, 0.4000, 0.2000;  % 橙色
    0.2000, 0.8000, 0.4000;  % 绿色
    0.8000, 0.2000, 0.6000;  % 紫红色
    0.6000, 0.8000, 0.2000;  % 黄绿色
    0.8000, 0.6000, 0.2000;  % 金色
    0.4000, 0.2000, 0.8000;  % 紫色
];

hold on;

% 设置坐标轴
battlefield_size = 10000;
xlim([0, battlefield_size]);
ylim([0, battlefield_size]);

% 绘制网格
grid on;
set(gca, 'GridAlpha', 0.3, 'GridLineStyle', '-');

% 绘制每个聚类
for i = 1:length(target_clusters)
    cluster = target_clusters{i};
    cluster_x = [Targets(cluster).x];
    cluster_y = [Targets(cluster).y];
    
    color_idx = mod(i-1, size(colors, 1)) + 1;
    current_color = colors(color_idx, :);
    
    % 绘制聚类边界（改进的凸包）
    if length(cluster) > 2
        try
            k = convhull(cluster_x, cluster_y);
            
            % 绘制半透明填充
            h_fill = fill(cluster_x(k), cluster_y(k), current_color);
            set(h_fill, 'FaceAlpha', 0.2, 'EdgeColor', current_color, ...
                'EdgeAlpha', 0.8, 'LineWidth', 3, 'LineStyle', '-');
            
        catch
            % 如果凸包失败，绘制圆形边界
            center_x = mean(cluster_x);
            center_y = mean(cluster_y);
            max_dist = max(sqrt((cluster_x - center_x).^2 + (cluster_y - center_y).^2));
            theta = linspace(0, 2*pi, 100);
            circle_x = center_x + max_dist * cos(theta);
            circle_y = center_y + max_dist * sin(theta);
            plot(circle_x, circle_y, '--', 'Color', current_color, 'LineWidth', 2);
        end
    end
    
    % 绘制目标点（根据威胁等级调整大小）
    for j = 1:length(cluster)
        target_id = cluster(j);
        threat_level = Targets(target_id).threat;
        
        % 威胁等级影响标记大小和样式
        if threat_level > 0.7
            marker_size = 12;
            marker_style = 's';  % 方形表示高威胁
        elseif threat_level > 0.4
            marker_size = 10;
            marker_style = 'o';  % 圆形表示中威胁
        else
            marker_size = 8;
            marker_style = '^';  % 三角形表示低威胁
        end
        
        % 绘制目标
        plot(Targets(target_id).x, Targets(target_id).y, marker_style, ...
            'MarkerSize', marker_size, 'MarkerEdgeColor', current_color, ...
            'MarkerFaceColor', current_color, 'LineWidth', 2);
        
        % 绘制速度向量（箭头）
        arrow_length = 200;
        quiver(Targets(target_id).x, Targets(target_id).y, ...
            arrow_length*cos(Targets(target_id).theta), ...
            arrow_length*sin(Targets(target_id).theta), ...
            0, 'Color', current_color, 'LineWidth', 2, 'MaxHeadSize', 0.4);
        
        % 添加目标编号
        text(Targets(target_id).x + 150, Targets(target_id).y + 150, ...
            sprintf('%d', target_id), 'FontSize', 9, 'Color', current_color, ...
            'FontWeight', 'bold', 'BackgroundColor', 'white', ...
            'EdgeColor', current_color, 'Margin', 2);
    end
    
    % 标记聚类中心
    center_x = mean(cluster_x);
    center_y = mean(cluster_y);
    plot(center_x, center_y, 'k*', 'MarkerSize', 15, 'LineWidth', 3);
    
    % 聚类标签
    text(center_x + 400, center_y + 400, ...
         sprintf('C%d\n(%d)', i, length(cluster)), ...
         'FontSize', 14, 'FontWeight', 'bold', 'Color', 'black', ...
         'BackgroundColor', 'white', 'EdgeColor', 'black', ...
         'HorizontalAlignment', 'center', 'Margin', 3);
end

% 设置图形属性
title('Feature Similarity Clustering Results', 'FontSize', 18, 'FontWeight', 'bold');
xlabel('X Coordinate (m)', 'FontSize', 16);
ylabel('Y Coordinate (m)', 'FontSize', 16);
set(gca, 'FontSize', 14);

% 创建专业图例
legend_elements = {};
legend_labels = {};
for i = 1:min(length(target_clusters), 5)
    color_idx = mod(i-1, size(colors, 1)) + 1;
    current_color = colors(color_idx, :);
    legend_elements{end+1} = plot(NaN, NaN, 'o', 'MarkerSize', 10, ...
        'MarkerFaceColor', current_color, 'MarkerEdgeColor', current_color, 'LineWidth', 2);
    legend_labels{end+1} = sprintf('Cluster %d (%d targets)', i, length(target_clusters{i}));
end

% 添加威胁等级图例
gray_color = [0.5, 0.5, 0.5];
legend_elements{end+1} = plot(NaN, NaN, 's', 'MarkerSize', 10, 'MarkerFaceColor', gray_color, 'MarkerEdgeColor', gray_color);
legend_labels{end+1} = 'High Threat';
legend_elements{end+1} = plot(NaN, NaN, 'o', 'MarkerSize', 10, 'MarkerFaceColor', gray_color, 'MarkerEdgeColor', gray_color);
legend_labels{end+1} = 'Medium Threat';
legend_elements{end+1} = plot(NaN, NaN, '^', 'MarkerSize', 10, 'MarkerFaceColor', gray_color, 'MarkerEdgeColor', gray_color);
legend_labels{end+1} = 'Low Threat';

if ~isempty(legend_elements)
    legend([legend_elements{:}], legend_labels, 'Location', 'northeast', ...
           'FontSize', 12, 'Box', 'on');
end

% 添加统计信息
cluster_sizes = cellfun(@length, target_clusters);
stats_text = sprintf(['Clustering Statistics:\n' ...
                     '• Number of clusters: %d\n' ...
                     '• Total targets: %d\n' ...
                     '• Average cluster size: %.1f\n' ...
                     '• Cluster size range: %d-%d\n' ...
                     '• Balance index: %.3f'], ...
                     length(target_clusters), length(Targets), ...
                     mean(cluster_sizes), min(cluster_sizes), max(cluster_sizes), ...
                     std(cluster_sizes)/mean(cluster_sizes));

annotation('textbox', [0.02, 0.02, 0.28, 0.18], 'String', stats_text, ...
           'FontSize', 11, 'BackgroundColor', 'white', 'EdgeColor', 'black', ...
           'FitBoxToText', 'on', 'Margin', 5);

hold off;

% 保存高质量图形
print(fig, 'Figure2_Professional_Clustering', '-dpng', '-r300');
saveas(fig, 'Figure2_Professional_Clustering.fig');

fprintf('专业级Figure 2已保存为:\n');
fprintf('  - Figure2_Professional_Clustering.png (高分辨率PNG)\n');
fprintf('  - Figure2_Professional_Clustering.fig (MATLAB格式)\n');

end

function W1 = calculateDistanceThreat(d, d_min, d_max)
% 计算基于距离的威胁度
if d <= d_min
    W1 = 1;
elseif d >= d_max
    W1 = 0;
else
    W1 = (d_max - d) / (d_max - d_min);
end
end



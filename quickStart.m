%% 快速启动脚本 - 多无人机任务分配系统
% 提供多种运行模式的快速启动选项

clear;
clc;
close all;

fprintf('=== 多无人机任务分配系统 ===\n');
fprintf('基于论文：Hierarchical Task Assignment for Multi-UAV System\n');
fprintf('in Large-Scale Group-to-Group Interception Scenarios\n\n');

%% 显示运行选项
fprintf('请选择运行模式：\n');
fprintf('1. 标准演示 - 运行完整的任务分配算法\n');
fprintf('2. 性能分析 - 详细性能指标分析\n');
fprintf('3. 参数敏感性分析 - 分析参数对性能的影响\n');
fprintf('4. 批量测试 - 多场景性能对比\n');
fprintf('5. 无人机生成模式测试 - 对比三种部署方式\n');
fprintf('6. 聚类算法测试 - 单独测试聚类功能\n');
fprintf('7. 退出\n\n');

choice = input('请输入选择 (1-7): ');

switch choice
    case 1
        runStandardDemo();
    case 2
        runPerformanceAnalysis();
    case 3
        runParameterSensitivity();
    case 4
        runBatchTesting();
    case 5
        runUAVGenerationTest();
    case 6
        runClusteringTest();
    case 7
        fprintf('退出程序。\n');
        return;
    otherwise
        fprintf('无效选择，运行标准演示。\n');
        runStandardDemo();
end

function runStandardDemo()
% 运行标准演示

fprintf('\n=== 运行标准演示 ===\n');
fprintf('选择无人机生成模式：\n');
fprintf('1. 随机生成\n');
fprintf('2. 三基地部署（推荐）\n');
fprintf('3. 高度集中\n');

uav_mode = input('请选择模式 (1-3): ');
if isempty(uav_mode) || uav_mode < 1 || uav_mode > 3
    uav_mode = 2;
    fprintf('使用默认模式：三基地部署\n');
end

% 修改main.m中的参数并运行
modifyMainParams(uav_mode);
fprintf('正在运行主程序...\n');
run('main.m');

end

function runPerformanceAnalysis()
% 运行性能分析

fprintf('\n=== 运行性能分析 ===\n');
fprintf('这将运行算法并生成详细的性能分析报告...\n');

% 使用固定参数确保结果可重现
modifyMainParams(2);  % 使用三基地部署
run('main.m');

end

function runParameterSensitivity()
% 运行参数敏感性分析

fprintf('\n=== 运行参数敏感性分析 ===\n');
fprintf('这将分析不同参数对算法性能的影响，可能需要几分钟时间...\n');

confirm = input('确认继续？(y/n): ', 's');
if lower(confirm) == 'y'
    parameterSensitivityAnalysis();
else
    fprintf('已取消参数敏感性分析。\n');
end

end

function runBatchTesting()
% 运行批量测试

fprintf('\n=== 运行批量测试 ===\n');
fprintf('这将在多个场景下测试算法性能，可能需要较长时间...\n');

confirm = input('确认继续？(y/n): ', 's');
if lower(confirm) == 'y'
    batchTesting();
else
    fprintf('已取消批量测试。\n');
end

end

function runUAVGenerationTest()
% 运行无人机生成模式测试

fprintf('\n=== 运行无人机生成模式测试 ===\n');
fprintf('选择测试类型：\n');
fprintf('1. 基本对比测试\n');
fprintf('2. 集中程度对比测试\n');
fprintf('3. 全模式快速测试\n');

test_type = input('请选择测试类型 (1-3): ');

switch test_type
    case 1
        run('test_uav_generation.m');
    case 2
        run('test_concentrated_uav.m');
    case 3
        run('test_all_modes.m');
    otherwise
        fprintf('无效选择，运行基本对比测试。\n');
        run('test_uav_generation.m');
end

end

function runClusteringTest()
% 运行聚类算法测试

fprintf('\n=== 运行聚类算法测试 ===\n');
fprintf('这将单独测试特征相似性聚类算法...\n');

run('test_clustering_only.m');

end

function modifyMainParams(uav_mode)
% 修改main.m中的无人机生成模式参数

% 读取main.m文件
filename = 'main.m';
fid = fopen(filename, 'r');
if fid == -1
    error('无法打开main.m文件');
end

% 读取所有行
lines = {};
while ~feof(fid)
    lines{end+1} = fgetl(fid);
end
fclose(fid);

% 查找并修改UAV_generation_mode行
for i = 1:length(lines)
    if contains(lines{i}, 'UAV_generation_mode = ')
        lines{i} = sprintf('UAV_generation_mode = %d;  %% <-- 在这里修改：1为随机，2为固定，3为高度集中', uav_mode);
        break;
    end
end

% 写回文件
fid = fopen(filename, 'w');
if fid == -1
    error('无法写入main.m文件');
end

for i = 1:length(lines)
    if ischar(lines{i})
        fprintf(fid, '%s\n', lines{i});
    end
end
fclose(fid);

fprintf('已设置无人机生成模式为：%d\n', uav_mode);

end

%% 显示帮助信息
function showHelp()

fprintf('\n=== 系统功能说明 ===\n');
fprintf('1. 标准演示：\n');
fprintf('   - 完整运行层次化任务分配算法\n');
fprintf('   - 包含聚类、分配、可视化等所有步骤\n');
fprintf('   - 适合首次使用和演示\n\n');

fprintf('2. 性能分析：\n');
fprintf('   - 详细的算法性能指标计算\n');
fprintf('   - 包含分配率、资源利用率、威胁覆盖等\n');
fprintf('   - 生成综合评分和性能等级\n\n');

fprintf('3. 参数敏感性分析：\n');
fprintf('   - 分析关键参数对算法性能的影响\n');
fprintf('   - 包含相似度阈值、聚类参数等\n');
fprintf('   - 帮助优化参数设置\n\n');

fprintf('4. 批量测试：\n');
fprintf('   - 在多种场景下测试算法性能\n');
fprintf('   - 包含不同规模、不同资源配置等\n');
fprintf('   - 评估算法的适应性和鲁棒性\n\n');

fprintf('5. 无人机生成模式测试：\n');
fprintf('   - 对比三种无人机部署方式\n');
fprintf('   - 分析集中程度对算法性能的影响\n');
fprintf('   - 帮助选择合适的部署策略\n\n');

fprintf('6. 聚类算法测试：\n');
fprintf('   - 单独测试特征相似性聚类算法\n');
fprintf('   - 验证聚类质量和参数设置\n');
fprintf('   - 适合算法调试和优化\n\n');

end

%% 显示系统状态
function showSystemStatus()

fprintf('\n=== 系统状态检查 ===\n');

% 检查必要文件
required_files = {
    'main.m', '主程序文件';
    'featureSimilarityClustering.m', '特征相似性聚类';
    'calculateInterceptionProbability.m', '拦截概率计算';
    'networkFlowTaskAssignment.m', '网络流任务分配';
    'performanceAnalysis.m', '性能分析模块';
    'enhancedVisualization.m', '增强可视化';
    'parameterSensitivityAnalysis.m', '参数敏感性分析';
    'batchTesting.m', '批量测试工具'
};

all_files_exist = true;
for i = 1:size(required_files, 1)
    if exist(required_files{i, 1}, 'file')
        fprintf('✓ %s - %s\n', required_files{i, 1}, required_files{i, 2});
    else
        fprintf('✗ %s - %s (缺失)\n', required_files{i, 1}, required_files{i, 2});
        all_files_exist = false;
    end
end

if all_files_exist
    fprintf('\n系统状态：正常 ✓\n');
else
    fprintf('\n系统状态：有文件缺失 ✗\n');
end

end

%% 如果直接运行此脚本，显示帮助信息
if ~exist('choice', 'var')
    showHelp();
    showSystemStatus();
end
